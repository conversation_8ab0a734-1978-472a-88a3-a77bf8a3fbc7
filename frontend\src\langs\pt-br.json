{"name": "Português", "common": {"confirm": "Confirmar", "cancel": "<PERSON><PERSON><PERSON>", "success": "Sucesso", "warning": "Aviso", "error": "Erro", "save": "<PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON><PERSON>", "none": "<PERSON><PERSON><PERSON>", "second": "<PERSON><PERSON><PERSON>(s)", "minute": "<PERSON><PERSON>(s)", "hour": "<PERSON><PERSON>(s)", "day": "Dia(s)", "unit_day": "D", "unit_hour": "H", "unit_minute": "M", "unit_second": "S", "all": "<PERSON><PERSON>", "key": "Chave", "value": "Valor", "field": "Campo", "score": "Pontuação", "index": "Posição"}, "preferences": {"name": "Preferências", "restore_defaults": "<PERSON><PERSON><PERSON>", "font_tip": "Suporta seleção múltipla. Digite manualmente a fonte se ela não estiver listada.", "general": {"name": "G<PERSON>", "theme": "<PERSON><PERSON>", "theme_light": "<PERSON><PERSON><PERSON>", "theme_dark": "Escuro", "theme_auto": "Automático", "language": "Idioma", "system_lang": "Usar Idioma do Sistema", "font": "Fonte", "font_tip": "Selecione ou digite o nome da fonte", "font_size": "<PERSON><PERSON><PERSON>", "scan_size": "Tamanho <PERSON>drão para Comando SCAN", "scan_size_tip": "Número de elementos retornados por vez pelos comandos SCAN/HSCAN/SSCAN/ZSCAN", "key_icon_style": "Estilo do Ícone de Chave", "key_icon_style0": "Compacto", "key_icon_style1": "Nome <PERSON>to", "key_icon_style2": "Ponto", "key_icon_style3": "Comum", "update": "<PERSON><PERSON><PERSON><PERSON>", "auto_check_update": "Verificar atualizações automaticamente", "privacy": "Política de Privacidade", "allow_track": "Permitir a coleta de dados anônimos"}, "editor": {"name": "Editor", "show_linenum": "Mostrar Números de Linha", "show_folding": "Habilitar Dobra de Código", "drop_text": "Permitir <PERSON> e Soltar Texto", "links": "Suportar Links"}, "cli": {"name": "<PERSON><PERSON>", "cursor_style": "<PERSON>st<PERSON>ursor", "cursor_style_block": "Bloco", "cursor_style_underline": "<PERSON><PERSON><PERSON><PERSON>", "cursor_style_bar": "Barr<PERSON>"}, "decoder": {"name": "Decodificador Personalizado", "new": "Novo Decodificador", "decoder_name": "Nome", "cmd_preview": "Visualizar", "status": "Status", "auto_enabled": "Decodificação Automática Habilitada", "help": "<PERSON><PERSON><PERSON>"}}, "interface": {"new_conn": "Adicionar Nova Conexão", "new_group": "Adicionar Novo Grupo", "disconnect_all": "Desconectar Tudo", "status": "Status", "filter": "Filtro", "sort_conn": "Ordenar Conexões", "new_conn_title": "Nova Conexão", "open_db": "Abrir Banco de Dados", "close_db": "<PERSON><PERSON><PERSON>", "filter_key": "Filtrar Chave", "disconnect": "Desconectar", "dup_conn": "<PERSON><PERSON><PERSON><PERSON>", "remove_conn": "Excluir <PERSON>", "edit_conn": "Editar Configuração da Conexão", "edit_conn_group": "Editar Grupo de Conexão", "rename_conn_group": "Renomear Grupo de Conexão", "remove_conn_group": "Excluir Grupo de Conexão", "import_conn": "Importar Conexões...", "export_conn": "Exportar Conexões...", "ttl": "TTL", "forever": "Para Sempre", "rename_key": "Renomear Chave", "delete_key": "Excluir Chave", "batch_delete_key": "Excluir <PERSON>", "import_key": "Importar <PERSON>", "flush_db": "Limpar Banco de Dados", "check_mode": "Modo de Seleção", "quit_check_mode": "Sair do Modo de Seleção", "delete_checked": "Excluir Selecionados", "export_checked": "Exportar Selecionados", "ttl_checked": "Atualizar TTL para Selecionados", "copy_value": "<PERSON><PERSON><PERSON>", "edit_value": "<PERSON><PERSON>", "save_update": "<PERSON><PERSON>", "score_filter_tip": "Lista de operadores suportados abaixo:\n= igual\n!= diferente\n> maior que\n>= maior ou igual a\n< menor que\n<= menor ou igual a\nPor exemplo, se você deseja filtrar resultados maiores que 3, insira: >3", "add_row": "<PERSON><PERSON><PERSON><PERSON>", "edit_row": "<PERSON><PERSON>", "delete_row": "<PERSON>cluir <PERSON>", "fullscreen": "Tela Cheia", "offscreen": "<PERSON><PERSON> <PERSON>", "pin_edit": "Fixar (Permanecer a<PERSON> salvar)", "unpin_edit": "Desafixar", "search": "Buscar", "full_search": "Busca Completa", "full_search_result": "Conte<PERSON>do <PERSON> '{pattern}'", "filter_field": "Filtrar Campo", "filter_value": "Filtrar Valor", "length": "<PERSON><PERSON><PERSON>", "entries": "Entradas", "memory_usage": "Uso de Memória", "text_align_left": "Alinhar à esquerda", "text_align_center": "Centralizar", "view_as": "Visualizar Como", "decode_with": "Decodificar / Descompressão", "custom_decoder": "Novo Decodificador Personalizado", "reload": "<PERSON><PERSON><PERSON><PERSON>", "reload_disable": "Recarregar após carregar completamente", "auto_refresh": "Atualização Automática", "refresh_interval": "Intervalo de Atualização", "open_connection": "<PERSON><PERSON><PERSON>", "copy_path": "<PERSON><PERSON><PERSON>", "copy_key": "<PERSON><PERSON><PERSON>", "save_value_succ": "Valor Salvo!", "copy_succ": "Copiado para a Área de Transferência!", "binary_key": "Nome da Chave Binária", "remove_key": "Remover Chave", "new_key": "<PERSON><PERSON><PERSON><PERSON>", "load_more": "<PERSON><PERSON><PERSON>", "load_all": "<PERSON><PERSON><PERSON> as <PERSON><PERSON>", "load_more_entries": "<PERSON><PERSON><PERSON>", "load_all_entries": "<PERSON><PERSON><PERSON>", "more_action": "Mais A<PERSON>", "nonexist_tab_content": "A chave selecionada não existe ou nenhuma chave está selecionada. Tente novamente após atualizar.", "empty_server_content": "Selecione e abra uma conexão à esquerda", "empty_server_list": "Nenhum servidor Redis adicionado", "action": "Ação", "type": "Tipo", "cli_welcome": "Bem-vindo ao Console Redis Tiny RDM", "retrieving_version": "Verificando atualizações", "sub_tab": {"status": "Status", "key_detail": "<PERSON><PERSON><PERSON> da Chave", "cli": "<PERSON><PERSON><PERSON>", "slow_log": "<PERSON><PERSON>", "cmd_monitor": "<PERSON><PERSON>", "pub_message": "Pub/Sub"}}, "ribbon": {"server": "<PERSON><PERSON><PERSON>", "browser": "Navegador de <PERSON>", "log": "Log", "wechat_official": "Conta Oficial do WeChat", "follow_x": "Siga 𝕏", "github": "<PERSON><PERSON><PERSON>"}, "dialogue": {"close_confirm": "<PERSON><PERSON><PERSON> esta <PERSON> ({name})?", "edit_close_confirm": "Por favor, feche as conexões relevantes antes de editar. Deseja continuar?", "opening_connection": "<PERSON><PERSON><PERSON>...", "interrupt_connection": "<PERSON><PERSON><PERSON>", "remove_tip": "{type} \"{name}\" será excluído", "remove_group_tip": "O grupo \"{name}\" e todas as conexões nele serão excluídos", "rename_binary_key_fail": "Renomear nome de chave binária não é suportado", "handle_succ": "Sucesso!", "handle_cancel": "Operação cancelada.", "reload_succ": "<PERSON><PERSON><PERSON><PERSON>!", "field_required": "Este campo é obrigatório", "spec_field_required": "\"{key}\" <PERSON> obrigatório", "illegal_characters": "Contém caracteres ilegais", "connection": {"new_title": "Nova Conexão", "edit_title": "<PERSON><PERSON>", "general": "G<PERSON>", "no_group": "Sem Grupo", "group": "Grupo", "conn_name": "Nome", "addr": "Endereço", "usr": "Nome de Usuário", "pwd": "<PERSON><PERSON>", "name_tip": "Nome da Conexão", "addr_tip": "Endereço do servidor Redis", "sock_tip": "Arquivo de socket unix do Redis", "usr_tip": "(Opcional) Nome de usuário para autenticação", "pwd_tip": "(Opcional) Senha de autenticação (Redis > 6.0)", "test": "<PERSON><PERSON>", "test_succ": "Conectado com sucesso ao servidor Redis", "test_fail": "Falha na Conexão", "parse_url_clipboard": "Analisar URL da Área de Transferência", "parse_pass": "URL Redis analisada: {url}", "parse_fail": "Falha ao analisar URL Redis: {reason}", "advn": {"title": "Avançado", "filter": "Filtro Padrão de Chave", "filter_tip": "Padrão que define as chaves carregadas do servidor Redis", "separator": "Separador de Chave", "separator_tip": "Separador para segmento do caminho da chave", "conn_timeout": "Tempo Limite de Conexão", "exec_timeout": "Tempo Limite de Execução", "dbfilter_type": "Filtro de Banco de Dados", "dbfilter_all": "<PERSON><PERSON>", "dbfilter_show": "Mostrar <PERSON>", "dbfilter_hide": "Ocultar Selecionados", "dbfilter_show_title": "Bancos de Dados a Mostrar", "dbfilter_hide_title": "Bancos de Dados a Ocultar", "dbfilter_input": "Índice do Banco de Dados de Entrada", "dbfilter_input_tip": "Pressione Enter para confirmar", "key_view": "Visualização Padrão de Chave", "key_view_tree": "Visualização em Árvore", "key_view_list": "Visualização em Lista", "load_size": "<PERSON><PERSON>", "mark_color": "Cor de Marcação"}, "alias": {"title": "Alias do Banco de Dados", "db": "Índice do Banco de Dados de Entrada", "value": "Alias do Banco de Dados de Entrada"}, "ssl": {"title": "SSL/TLS", "enable": "Habilitar SSL/TLS", "allow_insecure": "<PERSON><PERSON><PERSON>", "sni": "Nome do Servidor (SNI)", "sni_tip": "(Opcional) Nome do servidor", "cert_file": "Arquivo de Chave Pública", "key_file": "Arquivo de Chave Privada", "ca_file": "Arquivo CA", "cert_file_tip": "Arquivo de Chave Pública no formato PEM (Cert)", "key_file_tip": "Arquivo de Chave Privada no formato PEM (Chave)", "ca_file_tip": "Arquivo de Autoridade de Certificação no formato PEM (CA)"}, "ssh": {"enable": "Habilitar Túnel SSH", "title": "Túnel SSH", "login_type": "<PERSON><PERSON><PERSON>", "pkfile": "Arquivo de Chave Privada", "passphrase": "<PERSON><PERSON>", "addr_tip": "Endereço do Servidor SSH", "usr_tip": "Nome de Usuário <PERSON>", "pwd_tip": "Senha SSH", "pkfile_tip": "Caminho do Arquivo de Chave Privada SSH", "passphrase_tip": "(Opcional) Frase de Senha para Chave Privada"}, "sentinel": {"title": "Sentinela", "enable": "Atuar como Nó Sentinela", "master": "Nome do Grupo Master", "auto_discover": "Auto Descoberta", "password": "Senha para Nó Master", "username": "Nome de Usuário para Nó Master", "pwd_tip": "(Opcional) Senha de autenticação no nó master (Redis > 6.0)", "usr_tip": "(Opcional) Nome de usuário para autenticação no nó master"}, "cluster": {"title": "Cluster", "enable": "Atuar como Nó Cluster"}, "proxy": {"title": "Proxy", "type_none": "Sem Proxy", "type_system": "Proxy do Sistema", "type_custom": "Proxy Manual", "host": "Nome do Host", "auth": "Autenticação de Proxy", "usr_tip": "Nome de usuário para autenticação de proxy", "pwd_tip": "Senha para autenticação de proxy"}}, "group": {"name": "Nome do Grupo", "rename": "Renomear Grupo", "new": "Novo Grupo"}, "key": {"new": "Nova Chave", "new_name": "Novo Nome da Chave", "server": "Conexão", "db_index": "Índice do Banco de Dados", "key_expression": "Expressão da Chave", "affected_key": "<PERSON><PERSON>", "show_affected_key": "<PERSON><PERSON>", "confirm_delete_key": "Confirmar <PERSON><PERSON> {num} Chave(s)", "direct_delete": "Excluir padrão correspondente diretamente", "confirm_delete": "Confirmar exclusão", "async_delete": "Execução Assíncrona", "async_delete_title": "Não esperar pelo resultado da operação", "confirm_flush": "Eu sei o que estou fazendo!", "confirm_flush_db": "Confirmar <PERSON>"}, "delete": {"success": "\"{key}\" exclu<PERSON>da", "deleting": "Excluindo", "doing": "Excluindo chave ({index}/{count})", "completed": "Exclusão concluída, {success} realizadas com sucesso, {fail} falharam"}, "field": {"new": "Novo Campo", "new_item": "Novo Item", "conflict_handle": "Em Conflito de Campo", "overwrite_field": "Sobrescrever", "ignore_field": "<PERSON><PERSON><PERSON>", "insert_type": "Tipo de Inserção", "append_item": "Anexar", "prepend_item": "Inserir no Início", "enter_key": "Digite a Chave", "enter_value": "Digite o Valor", "enter_field": "Digite o Nome do Campo", "enter_elem": "Digite o Elemento", "enter_member": "Digite o Membro", "enter_score": "Digite a Pontuação", "element": "Elemento", "reload_when_succ": "Recarregar imediatamente após o sucesso"}, "filter": {"set_key_filter": "Definir Filtro de Chave", "filter_pattern": "Padrão", "filter_pattern_tip": "Filtre a lista atual inserindo diretamente, e escaneie o servidor pressionando 'Enter'.\n\n* corresponde a 0 ou mais caracteres, ex: 'chave*'\n? corresponde a um único caractere, ex: 'chave?'\n[] corresponde a um intervalo, ex: 'chave[1-3]'\n\\ escapa caracteres especiais", "exact_match_tip": "Correspondência Exata", "filter_type_not_support": "A filtragem por tipo não é suportada para Redis 5.x e versões anteriores"}, "export": {"name": "Exportar Dados", "export_expire_title": "Expiração", "export_expire": "Incluir Expiração", "export": "Exportar", "save_file": "Caminho de Exportação", "save_file_tip": "Selecione o caminho para salvar o arquivo exportado", "exporting": "Exportando chaves ({index}/{count})", "export_completed": "Exportação concluída, {success} realizadas com sucesso, {fail} falharam"}, "import": {"name": "Importar Dados", "import_expire_title": "Expiração", "import": "Importar", "reload": "Recarregar Após Importar", "open_csv_file": "Arquivo de Importação", "open_csv_file_tip": "Selecione o arquivo para importar", "conflict_handle": "<PERSON>flit<PERSON>ve", "conflict_overwrite": "Sobrescrever", "conflict_ignore": "<PERSON><PERSON><PERSON>", "ttl_include": "Importar Do Arquivo", "ttl_ignore": "Não Definir", "ttl_custom": "Personalizado", "importing": "Importando chaves importadas/sobrescritas:{imported} conflito/falha:{conflict}", "import_completed": "Importação concluída, {success} realizadas com sucesso, {ignored} ignoradas"}, "ttl": {"title": "Atualizar TTL", "title_batch": "Atualização em Lote de TTL ({count})", "quick_set": "Definir Rapidamente", "success": "TTL atualizado para todas as chaves"}, "decoder": {"name": "Novo Decodificador/Codificador", "edit_name": "Editar Decodificador/Codificador", "new": "Novo", "decoder": "Decodificador", "encoder": "Codificador", "decoder_name": "Nome", "auto": "Decodificação Automática", "decode_path": "Caminho do Decodificador", "encode_path": "Caminho do Codificador", "path_help": "<PERSON><PERSON><PERSON> para execut<PERSON><PERSON> ou alias de cli como 'sh/php/python'", "args": "Argumentos", "args_help": "Use [VALUE] como espaço reservado para conteúdo de codificação/decodificação. O conteúdo será anexado ao final se nenhum espaço reservado for fornecido."}, "upgrade": {"title": "Nova Versão Disponível", "new_version_tip": "Nova versão {ver} disponível, baixar agora?", "no_update": "Você está atualizado", "download_now": "Baixar Agora", "later": "<PERSON><PERSON><PERSON>", "skip": "Ignorar <PERSON> Versão"}, "welcome": {"title": "Bem-vindo ao Tiny RDM!", "content": "Para fornecer uma melhor experiência ao usuário, o Tiny RDM coleta alguns dados anônimos para ajudar a otimizar o software e melhorar a experiência do usuário. Fique tranquilo, isso não envolve suas informações de privacidade pessoal.\n\nSe você tiver alguma preocupação, pode desativar esse recurso de coleta de dados a qualquer momento indo em Preferências. Se tiver alguma dúvida, sinta-se à vontade para entrar em contato com o desenvolvedor. Espero que o Tiny RDM possa se tornar um assistente útil para você!", "accept": "Help Improve", "reject": "Reject"}, "about": {"source": "<PERSON><PERSON><PERSON>", "website": "Site Oficial"}}, "menu": {"minimise": "<PERSON><PERSON><PERSON>", "maximise": "Maximizar", "restore": "Restaurar", "close": "<PERSON><PERSON><PERSON>", "preferences": "Preferências", "help": "<PERSON><PERSON><PERSON>", "user_guide": "Guia do Usuário", "check_update": "Verificar Atualizações...", "report_bug": "<PERSON><PERSON>", "about": "Sobre"}, "log": {"title": "Log de Inicialização", "filter_server": "<PERSON><PERSON><PERSON> Servidor", "filter_keyword": "Filtrar Palavra-chave", "clean_log": "Limpar Log", "confirm_clean_log": "Confirmar limpar log de inicialização", "exec_time": "Tempo de Execução", "server": "<PERSON><PERSON><PERSON>", "cmd": "Comand<PERSON>", "cost_time": "Custo", "refresh": "<PERSON><PERSON><PERSON><PERSON>"}, "status": {"uptime": "Tempo de Atividade", "connected_clients": "Clientes Conectados", "total_keys": "Total de Chaves", "memory_used": "Memória Usada", "server_info": "Informações do Servidor", "activity_status": "Status da Atividade", "act_cmd": "Comandos/Seg", "act_network_input": "Entrada de Rede", "act_network_output": "<PERSON><PERSON><PERSON> de <PERSON>e", "client": {"title": "Lista de Clientes", "addr": "Endereço do Cliente", "age": "<PERSON><PERSON> (seg)", "idle": "<PERSON><PERSON><PERSON> (seg)", "db": "Banco de Dados"}}, "slog": {"title": "<PERSON><PERSON>", "limit": "Limite", "filter": "Filtrar", "exec_time": "Tempo", "client": "Cliente", "cmd": "Comand<PERSON>", "cost_time": "Custo"}, "monitor": {"title": "<PERSON><PERSON>", "actions": "Ações", "warning": "O monitoramento de comandos pode causar bloqueio do servidor, use com cuidado em servidores de produção.", "start": "Iniciar", "stop": "<PERSON><PERSON>", "search": "Buscar", "copy_log": "<PERSON><PERSON><PERSON>", "save_log": "<PERSON><PERSON>", "clean_log": "Limpar Log", "always_show_last": "Rolar automaticamente para o mais recente"}, "pubsub": {"title": "Pub/Sub", "publish": "Publicar", "subscribe": "Inscrever", "unsubscribe": "<PERSON><PERSON>ar Inscrição", "clear": "Limpar <PERSON>", "time": "Tempo", "filter": "Filtrar", "channel": "Canal", "message": "Mensagem", "receive_message": "<PERSON><PERSON><PERSON><PERSON> {total} mensagens", "always_show_last": "Rolar automaticamente para o mais recente"}}