{"name": "한국어", "common": {"confirm": "확인", "cancel": "취소", "success": "성공", "warning": "경고", "error": "오류", "save": "저장", "update": "업데이트", "none": "없음", "second": "초", "minute": "분", "hour": "시간", "day": "일", "unit_day": "일", "unit_hour": "시간", "unit_minute": "분", "unit_second": "초", "all": "전체", "key": "키", "value": "값", "field": "필드", "score": "점수", "index": "위치"}, "preferences": {"name": "설정", "restore_defaults": "기본값 복원", "font_tip": "다중 선택 지원, 목록에 없는 폰트는 직접 입력하세요", "general": {"name": "일반", "theme": "테마", "theme_light": "밝은 테마", "theme_dark": "어두운 테마", "theme_auto": "자동", "language": "언어", "system_lang": "시스템 언어 사용", "font": "폰트", "font_tip": "폰트 선택 또는 이름 입력", "font_size": "폰트 크기", "scan_size": "SCAN 기본 크기", "scan_size_tip": "SCAN/HSCAN/SSCAN/ZSCAN 명령에서 한 번에 반환되는 요소 수", "key_icon_style": "키 아이콘 스타일", "key_icon_style0": "간략한 타입", "key_icon_style1": "전체 이름", "key_icon_style2": "점 타입", "key_icon_style3": "일반 아이콘", "update": "업데이트", "auto_check_update": "자동 업데이트 확인", "privacy": "개인 정보 보호 정책", "allow_track": "익명 데이터 수집 허용"}, "editor": {"name": "에디터", "show_linenum": "줄번호 표시", "show_folding": "코드 폴딩 활성화", "drop_text": "텍스트 드래그 앤 드롭 허용", "links": "링크 지원"}, "cli": {"name": "명령줄", "cursor_style": "커서 스타일", "cursor_style_block": "블록", "cursor_style_underline": "밑줄", "cursor_style_bar": "바"}, "decoder": {"name": "사용자 정의 디코더", "new": "새 디코더", "decoder_name": "이름", "cmd_preview": "미리보기", "status": "상태", "auto_enabled": "자동 디코딩 활성화", "help": "도움말"}}, "interface": {"new_conn": "새 연결 추가", "new_group": "새 그룹 추가", "disconnect_all": "모든 연결 끊기", "status": "상태", "filter": "필터", "sort_conn": "연결 정렬", "new_conn_title": "새 연결", "open_db": "데이터베이스 열기", "close_db": "데이터베이스 닫기", "filter_key": "키 필터링", "disconnect": "연결 끊기", "dup_conn": "연결 복제", "remove_conn": "연결 제거", "edit_conn": "연결 편집", "edit_conn_group": "그룹 편집", "rename_conn_group": "그룹 이름 변경", "remove_conn_group": "그룹 제거", "import_conn": "연결 가져오기...", "export_conn": "연결 내보내기...", "ttl": "TTL", "forever": "영구", "rename_key": "키 이름 변경", "delete_key": "키 삭제", "batch_delete_key": "키 일괄 삭제", "import_key": "키 가져오기", "flush_db": "데이터베이스 플러시", "check_mode": "선택 모드", "quit_check_mode": "선택 모드 종료", "delete_checked": "선택 항목 삭제", "export_checked": "선택 항목 내보내기", "ttl_checked": "선택 항목 TTL 업데이트", "copy_value": "값 복사", "edit_value": "값 편집", "save_update": "변경사항 저장", "score_filter_tip": "다음 연산자로 범위 일치 가능\n=: 같음\n!=: 다름\n>: 큼\n<: 작음\n>=: 크거나 같음\n<=: 작거나 같음\n예) 점수가 3 이상인 결과: >3", "add_row": "행 삽입", "edit_row": "행 편집", "delete_row": "행 삭제", "fullscreen": "전체화면", "offscreen": "전체화면 종료", "pin_edit": "편집기 고정(저장 후 열린 상태 유지)", "unpin_edit": "고정 해제", "search": "검색", "full_search": "전체 텍스트 검색", "full_search_result": "'{pattern}'와 일치하는 내용", "filter_field": "필드 필터링", "filter_value": "값 필터링", "length": "길이", "entries": "항목 수", "memory_usage": "메모리 사용량", "text_align_left": "텍스트 왼쪽 정렬", "text_align_center": "텍스트 가운데 정렬", "view_as": "보기", "decode_with": "디코딩/압축 해제", "custom_decoder": "새 사용자 정의 디코더", "reload": "새로고침", "reload_disable": "전체 로드 후 새로고침 가능", "auto_refresh": "자동 새로고침", "refresh_interval": "새로고침 간격", "open_connection": "연결 열기", "copy_path": "경로 복사", "copy_key": "키 복사", "save_value_succ": "값 저장됨!", "copy_succ": "클립보드에 복사됨!", "binary_key": "바이너리 키 이름", "remove_key": "키 제거", "new_key": "새 키", "load_more": "더 많은 키 불러오기", "load_all": "남은 모든 키 불러오기", "load_more_entries": "더 불러오기", "load_all_entries": "모두 불러오기", "more_action": "더 많은 작업", "nonexist_tab_content": "선택한 키가 없거나 존재하지 않습니다. 새로고침 후 다시 시도하세요.", "empty_server_content": "왼쪽 패널에서 연결을 선택하고 열기", "empty_server_list": "추가된 Redis 서버 없음", "action": "작업", "type": "유형", "cli_welcome": "Tiny RDM Redis 콘솔 환영합니다", "retrieving_version": "업데이트 확인 중", "sub_tab": {"status": "상태", "key_detail": "키 상세정보", "cli": "콘솔", "slow_log": "슬로우 로그", "cmd_monitor": "명령 모니터링", "pub_message": "Pub/Sub"}}, "ribbon": {"server": "서버", "browser": "데이터 브라우저", "log": "로그", "wechat_official": "공식 계정", "follow_x": "팔로우 𝕏", "github": "<PERSON><PERSON><PERSON>"}, "dialogue": {"close_confirm": "이 연결({name})을 종료하시겠습니까?", "edit_close_confirm": "편집 전에 관련 연결을 종료해야 합니다. 계속하시겠습니까?", "opening_connection": "연결 중...", "interrupt_connection": "취소", "remove_tip": "{type} \"{name}\"가 삭제됩니다", "remove_group_tip": "그룹 \"{name}\"과 그 안의 모든 연결이 삭제됩니다", "rename_binary_key_fail": "바이너리 키 이름 변경은 지원되지 않습니다", "handle_succ": "성공!", "handle_cancel": "작업이 취소되었습니다.", "reload_succ": "새로고침 완료!", "field_required": "이 필드는 필수입니다", "spec_field_required": "\"{key}\"는 필수입니다", "illegal_characters": "잘못된 문자가 포함되어 있습니다", "connection": {"new_title": "새 연결", "edit_title": "연결 편집", "general": "일반", "no_group": "그룹 없음", "group": "그룹", "conn_name": "이름", "addr": "주소", "usr": "사용자 이름", "pwd": "비밀번호", "name_tip": "연결 이름", "addr_tip": "Redis 서버 주소", "sock_tip": "Redis 유닉스 소켓 파일", "usr_tip": "(선택) 인증 사용자 이름", "pwd_tip": "(선택) 인증 비밀번호 (Redis > 6.0)", "test": "연결 테스트", "test_succ": "Redis 서버에 성공적으로 연결되었습니다", "test_fail": "연결 실패", "parse_url_clipboard": "클립보드에서 URL 분석", "parse_pass": "Redis URL이 분석되었습니다: {url}", "parse_fail": "Redis URL 분석 실패: {reason}", "advn": {"title": "고급", "filter": "기본 키 필터", "filter_tip": "로드할 키 패턴", "separator": "키 구분 기호", "separator_tip": "키 경로 구분 기호", "conn_timeout": "연결 시간 초과", "exec_timeout": "실행 시간 초과", "dbfilter_type": "데이터베이스 필터", "dbfilter_all": "모두 표시", "dbfilter_show": "선택 항목 표시", "dbfilter_hide": "선택 항목 숨기기", "dbfilter_show_title": "표시할 데이터베이스", "dbfilter_hide_title": "숨길 데이터베이스", "dbfilter_input": "데이터베이스 인덱스 입력", "dbfilter_input_tip": "Enter를 눌러 확인", "key_view": "기본 키 보기", "key_view_tree": "트리 보기", "key_view_list": "목록 보기", "load_size": "불러올 키 수", "mark_color": "표시 색상"}, "alias": {"title": "데이터베이스 별칭", "db": "데이터베이스 인덱스 입력", "value": "별칭 입력"}, "ssl": {"title": "SSL/TLS", "enable": "SSL/TLS 활성화", "allow_insecure": "안전하지 않은 연결 허용", "sni": "서버 이름(SNI)", "sni_tip": "(선택) 서버 이름", "cert_file": "공개 키 파일", "key_file": "개인 키 파일", "ca_file": "CA 파일", "cert_file_tip": "PEM 형식 공개 키 파일(Cert)", "key_file_tip": "PEM 형식 개인 키 파일(Key)", "ca_file_tip": "PEM 형식 CA 파일(CA)"}, "ssh": {"enable": "SSH 터널 활성화", "title": "SSH 터널", "login_type": "로그인 유형", "pkfile": "개인 키 파일", "passphrase": "암호구문", "addr_tip": "SSH 주소", "usr_tip": "SSH 사용자 이름", "pwd_tip": "SSH 비밀번호", "pkfile_tip": "SSH 개인 키 파일 경로", "passphrase_tip": "(선택) SSH 개인 키 암호구문"}, "sentinel": {"title": "센티널 모드", "enable": "현재 센티널 노드", "master": "마스터 그룹 이름", "auto_discover": "자동 탐색", "password": "마스터 비밀번호", "username": "마스터 사용자 이름", "pwd_tip": "(선택) 마스터 인증 비밀번호 (Redis > 6.0)", "usr_tip": "(선택) 마스터 인증 사용자 이름"}, "cluster": {"title": "클러스터 모드", "enable": "현재 클러스터 노드"}, "proxy": {"title": "프록시", "type_none": "프록시 사용 안함", "type_system": "시스템 프록시 설정 사용", "type_custom": "프록시 수동 설정", "host": "호스트명", "auth": "인증 사용", "usr_tip": "프록시 인증 사용자 이름", "pwd_tip": "프록시 인증 비밀번호"}}, "group": {"name": "그룹 이름", "rename": "그룹 이름 변경", "new": "새 그룹"}, "key": {"new": "새 키", "new_name": "새 키 이름", "server": "연결", "db_index": "데이터베이스 인덱스", "key_expression": "키 패턴", "affected_key": "영향받는 키", "show_affected_key": "영향받는 키 표시", "confirm_delete_key": "{num}개의 키를 삭제하시겠습니까?", "direct_delete": "일치하는 패턴 직접 삭제", "confirm_delete": "삭제 확인", "async_delete": "비동기 실행", "async_delete_title": "결과를 기다리지 않음", "confirm_flush": "진행 중인 작업을 알고 있습니다!", "confirm_flush_db": "데이터베이스 플러시 확인"}, "delete": {"success": "\"{key}\"가 삭제되었습니다", "deleting": "삭제 중", "doing": "키 삭제 중 ({index}/{count})", "completed": "삭제가 완료되었습니다. 성공: {success}개, 실패: {fail}개"}, "field": {"new": "새 필드", "new_item": "새 항목", "conflict_handle": "필드 충돌 시", "overwrite_field": "덮어쓰기", "ignore_field": "무시", "insert_type": "삽입 유형", "append_item": "추가", "prepend_item": "앞에 추가", "enter_key": "키 입력", "enter_value": "값 입력", "enter_field": "필드 이름 입력", "enter_elem": "요소 입력", "enter_member": "멤버 입력", "enter_score": "점수 입력", "element": "요소", "reload_when_succ": "성공하면 즉시 새로고침"}, "filter": {"set_key_filter": "키 필터 설정", "filter_pattern": "패턴", "filter_pattern_tip": "직접 입력하여 현재 목록을 필터링하고, Enter키를 누르면 서버를 스캔할 수 있습니다.\n\n* 0개 이상의 문자 일치, 예) 'key*'\n? 단일 문자 일치, 예) 'key?'\n[] 범위 일치, 예) 'key[1-3]'\n\\ 특수문자 이스케이프", "exact_match_tip": "완전 일치", "filter_type_not_support": "타입 필터링은 Redis 5.x 및 이전 버전을 지원하지 않습니다"}, "export": {"name": "데이터 내보내기", "export_expire_title": "만료 시간", "export_expire": "만료 시간 포함", "export": "내보내기", "save_file": "내보내기 경로", "save_file_tip": "내보낼 파일 저장 경로 선택", "exporting": "키 내보내는 중 ({index}/{count})", "export_completed": "내보내기가 완료되었습니다. 성공: {success}개, 실패: {fail}개"}, "import": {"name": "데이터 가져오기", "import_expire_title": "만료 시간", "import": "가져오기", "reload": "가져오기 후 새로고침", "open_csv_file": "가져올 파일", "open_csv_file_tip": "가져올 파일 선택", "conflict_handle": "키 충돌 시", "conflict_overwrite": "덮어쓰기", "conflict_ignore": "무시", "ttl_include": "파일에서 가져오기", "ttl_ignore": "설정 안함", "ttl_custom": "직접 설정", "importing": "키 가져오는 중 가져오기/덮어쓰기:{imported} 충돌/실패:{conflict}", "import_completed": "가져오기가 완료되었습니다. 성공: {success}개, 무시: {ignored}개"}, "ttl": {"title": "TTL 업데이트", "title_batch": "TTL 일괄 업데이트 ({count})", "quick_set": "빠른 설정", "success": "모든 키의 TTL이 업데이트되었습니다"}, "decoder": {"name": "새 디코더/인코더", "edit_name": "디코더/인코더 편집", "new": "새로 만들기", "decoder": "디코더", "encoder": "인코더", "decoder_name": "이름", "auto": "자동 디코딩", "decode_path": "디코더 경로", "encode_path": "인코더 경로", "path_help": "실행 파일 경로 또는 sh/php/python과 같은 CLI 별칭", "args": "인수", "args_help": "[VALUE]를 인코딩/디코딩 내용 자리 표시자로 사용하세요. 자리 표시자가 없으면 끝에 추가됩니다."}, "upgrade": {"title": "새 버전 사용 가능", "new_version_tip": "새 버전 {ver}이 있습니다. 지금 다운로드하시겠습니까?", "no_update": "최신 버전입니다", "download_now": "지금 다운로드", "later": "나중에", "skip": "이 버전 건너뛰기"}, "welcome": {"title": "Tiny RDM에 오신 것을 환영합니다!", "content": "더 나은 사용자 경험을 제공하기 위해 Tiny RDM은 일부 익명 데이터를 수집하여 소프트웨어를 최적화하고 사용자 경험을 개선하는 데 사용합니다. 이는 개인 정보와는 무관함을 알려드립니다.\n\n만약 우려되는 점이 있다면 설정에서 이 데이터 수집 기능을 언제든 끌 수 있습니다. 문의 사항이 있으면 개발자에게 연락하시기 바랍니다. Tiny RDM이 좋은 도우미가 되길 바랍니다!", "accept": "개선에 동의", "reject": "거부"}, "about": {"source": "소스 코드", "website": "공식 웹사이트"}}, "menu": {"minimise": "최소화", "maximise": "최대화", "restore": "복원", "close": "닫기", "preferences": "설정", "help": "도움말", "user_guide": "사용자 가이드", "check_update": "업데이트 확인...", "report_bug": "버그 신고", "about": "정보"}, "log": {"title": "실행 로그", "filter_server": "서버 필터링", "filter_keyword": "키워드 필터링", "clean_log": "로그 지우기", "confirm_clean_log": "실행 로그를 지우시겠습니까?", "exec_time": "실행 시간", "server": "서버", "cmd": "명령", "cost_time": "소요 시간", "refresh": "새로고침"}, "status": {"uptime": "가동 시간", "connected_clients": "클라이언트 수", "total_keys": "키 수", "memory_used": "메모리 사용량", "server_info": "서버 정보", "activity_status": "활동 현황", "act_cmd": "명령/초", "act_network_input": "네트워크 입력", "act_network_output": "네트워크 출력", "client": {"title": "클라이언트 목록", "addr": "클라이언트 주소", "age": "시간(초)", "idle": "유휴 시간(초)", "db": "데이터베이스"}}, "slog": {"title": "슬로우 로그", "limit": "제한", "filter": "필터", "exec_time": "시간", "client": "클라이언트", "cmd": "명령", "cost_time": "소요 시간"}, "monitor": {"title": "명령 모니터링", "actions": "작업", "warning": "명령 모니터링은 서버 차단을 유발할 수 있으므로 실서버에서는 주의해서 사용하세요.", "start": "시작", "stop": "정지", "search": "검색", "copy_log": "로그 복사", "save_log": "로그 저장", "clean_log": "로그 지우기", "always_show_last": "최신 내용으로 자동 스크롤"}, "pubsub": {"title": "Pub/Sub", "publish": "발행", "subscribe": "구독", "unsubscribe": "구독 취소", "clear": "메시지 지우기", "time": "시간", "filter": "필터", "channel": "채널", "message": "메시지", "receive_message": "{total}개의 메시지를 받았습니다", "always_show_last": "최신 내용으로 자동 스크롤"}}