{"name": "Español", "common": {"confirm": "Confirmar", "cancel": "<PERSON><PERSON><PERSON>", "success": "Éxito", "warning": "Advertencia", "error": "Error", "save": "Guardar", "update": "Actualizar", "none": "<PERSON><PERSON><PERSON>", "second": "<PERSON><PERSON><PERSON>(s)", "minute": "<PERSON><PERSON>(s)", "hour": "<PERSON><PERSON>(s)", "day": "Día(s)", "unit_day": "d", "unit_hour": "h", "unit_minute": "m", "unit_second": "s", "all": "Todos", "key": "Clave", "value": "Valor", "field": "Campo", "score": "Puntuación", "index": "Posición"}, "preferences": {"name": "Preferencias", "restore_defaults": "Restaurar valores predeterminados", "font_tip": "Admite selección múltiple. Ingrese manualmente la fuente si no está en la lista.", "general": {"name": "General", "theme": "<PERSON><PERSON>", "theme_light": "<PERSON><PERSON><PERSON>", "theme_dark": "Oscuro", "theme_auto": "Automático", "language": "Idioma", "system_lang": "Usar el idioma del sistema", "font": "Fuente", "font_tip": "Seleccione o ingrese el nombre de la fuente", "font_size": "Tamaño de fuente", "scan_size": "Tamaño predeterminado para SCAN", "scan_size_tip": "Número de elementos devueltos por los comandos SCAN/HSCAN/SSCAN/ZSCAN", "key_icon_style": "Estilo de icono de clave", "key_icon_style0": "Compacto", "key_icon_style1": "Nombre completo", "key_icon_style2": "Punt<PERSON>", "key_icon_style3": "Común", "update": "Actualizar", "auto_check_update": "Buscar actualizaciones automáticamente", "privacy": "Política de Privacidad", "allow_track": "<PERSON><PERSON><PERSON> recopilar datos anónimos"}, "editor": {"name": "Editor", "show_linenum": "Mostrar números de línea", "show_folding": "Habilitar plegado de código", "drop_text": "<PERSON><PERSON><PERSON> y soltar texto", "links": "Compatibilidad con enlaces"}, "cli": {"name": "Línea de comandos", "cursor_style": "<PERSON><PERSON><PERSON> del cursor", "cursor_style_block": "Bloque", "cursor_style_underline": "Subrayado", "cursor_style_bar": "Barr<PERSON>"}, "decoder": {"name": "Decodificador personalizado", "new": "Nuevo decodificador", "decoder_name": "Nombre", "cmd_preview": "Vista previa", "status": "Estado", "auto_enabled": "Decodificación automática habilitada", "help": "<PERSON><PERSON><PERSON>"}}, "interface": {"new_conn": "Agregar <PERSON>", "new_group": "Agregar grupo", "disconnect_all": "Desconectar todo", "status": "Estado", "filter": "Filtrar", "sort_conn": "Ordenar conexiones", "new_conn_title": "Nueva conexión", "open_db": "Abrir base de datos", "close_db": "Cerrar base de datos", "filter_key": "Filtrar claves", "disconnect": "Desconectar", "dup_conn": "Duplicar conexión", "remove_conn": "Eliminar conexión", "edit_conn": "<PERSON><PERSON>", "edit_conn_group": "Editar grupo", "rename_conn_group": "Renombrar grupo", "remove_conn_group": "Eliminar grupo", "import_conn": "Importar conexiones...", "export_conn": "Exportar conexiones...", "ttl": "TTL", "forever": "Siempre", "rename_key": "Renombrar clave", "delete_key": "Eliminar clave", "batch_delete_key": "Eliminar claves en lote", "import_key": "Importar claves", "flush_db": "Vaciar base de datos", "check_mode": "Modo de selección", "quit_check_mode": "Salir del modo de selección", "delete_checked": "Eliminar seleccionados", "export_checked": "Exportar seleccionados", "ttl_checked": "Actualizar TTL para seleccionados", "copy_value": "Copiar valor", "edit_value": "Editar valor", "save_update": "Guardar cambios", "score_filter_tip": "Soporte para operadores:\n= igual\n!= distinto\n> mayor que\n>= mayor o igual\n< menor que\n<= menor o igual\ne.j. >3 para puntuaciones mayores que 3", "add_row": "Insertar fila", "edit_row": "<PERSON><PERSON> fila", "delete_row": "Eliminar fila", "fullscreen": "Pantalla completa", "offscreen": "Salir de pantalla completa", "pin_edit": "Fijar (permanece abierto después de guardar)", "unpin_edit": "<PERSON><PERSON><PERSON>", "search": "Buscar", "full_search": "Búsqueda de texto completo", "full_search_result": "Contenido coincidente '{pattern}'", "filter_field": "Filtrar campo", "filter_value": "Filtrar valor", "length": "<PERSON><PERSON><PERSON>", "entries": "Entradas", "memory_usage": "Uso de memoria", "text_align_left": "Alinear a la izquierda", "text_align_center": "Centrar", "view_as": "Ver como", "decode_with": "Decodificar / Descomprimir", "custom_decoder": "Nuevo decodificador personalizado", "reload": "Recargar", "reload_disable": "Recargar después de cargar completamente", "auto_refresh": "Actualización automática", "refresh_interval": "Intervalo de actualización", "open_connection": "<PERSON><PERSON><PERSON>", "copy_path": "Copiar ruta", "copy_key": "<PERSON><PERSON><PERSON> clave", "save_value_succ": "¡Valor guardado!", "copy_succ": "¡Copiado al portapapeles!", "binary_key": "Clave binaria", "remove_key": "Eliminar clave", "new_key": "Nueva clave", "load_more": "<PERSON>gar más claves", "load_all": "<PERSON><PERSON> todas las claves restantes", "load_more_entries": "<PERSON>gar más", "load_all_entries": "<PERSON><PERSON> todo", "more_action": "Más acciones", "nonexist_tab_content": "La clave seleccionada no existe o ninguna seleccionada. Intente nuevamente después de actualizar.", "empty_server_content": "Seleccione y abra una conexión desde el panel izquierdo", "empty_server_list": "No se ha agregado ningún servidor <PERSON>", "action": "Acción", "type": "Tipo", "cli_welcome": "Bienvenido a la consola Redis de Tiny RDM", "retrieving_version": "Buscando actualizaciones", "sub_tab": {"status": "Estado", "key_detail": "Detalles de clave", "cli": "Consola", "slow_log": "Registro lento", "cmd_monitor": "Monitorear comandos", "pub_message": "Pub/Sub"}}, "ribbon": {"server": "<PERSON><PERSON><PERSON>", "browser": "Explorador de datos", "log": "Registro", "wechat_official": "Cuenta oficial de WeChat", "follow_x": "<PERSON><PERSON><PERSON> 𝕏", "github": "<PERSON><PERSON><PERSON>"}, "dialogue": {"close_confirm": "¿Cerrar esta conexión ({name})?", "edit_close_confirm": "Cierre las conexiones relevantes antes de editar. ¿Continuar?", "opening_connection": "Abriendo conexión...", "interrupt_connection": "<PERSON><PERSON><PERSON>", "remove_tip": "{type} \"{name}\" será eliminado", "remove_group_tip": "El grupo \"{name}\" y todas sus conexiones serán eliminados", "rename_binary_key_fail": "No se admite renombrar claves binarias", "handle_succ": "¡Éxito!", "handle_cancel": "Operación cancelada.", "reload_succ": "¡Recargado!", "field_required": "Este campo es obligatorio", "spec_field_required": "\"{key}\" es obligatorio", "illegal_characters": "Con<PERSON>e caracteres ilegales", "connection": {"new_title": "Nueva conexión", "edit_title": "<PERSON><PERSON>", "general": "General", "no_group": "Sin grupo", "group": "Grupo", "conn_name": "Nombre", "addr": "Dirección", "usr": "Usuario", "pwd": "Contraseña", "name_tip": "Nombre de la conexión", "addr_tip": "Dirección del servidor Redis", "sock_tip": "Archivo de socket Unix de Redis", "usr_tip": "(Opcional) Usuario de autenticación", "pwd_tip": "(Opcional) Contraseña de autenticación (Redis > 6.0)", "test": "Probar conexi<PERSON>", "test_succ": "Conectado con éxito al servidor Redis", "test_fail": "Falló la conexión", "parse_url_clipboard": "Analizar URL desde el portapapeles", "parse_pass": "URL de Redis analizada: {url}", "parse_fail": "Error al analizar la URL de Redis: {reason}", "advn": {"title": "<PERSON><PERSON><PERSON>", "filter": "Filtro de clave predeterminado", "filter_tip": "Patrón para filtrar las claves cargadas", "separator": "Separador de clave", "separator_tip": "Separador para segmentos de ruta de clave", "conn_timeout": "Tiempo de espera de conexión", "exec_timeout": "Tiempo de espera de ejecución", "dbfilter_type": "Filtro de base de datos", "dbfilter_all": "<PERSON><PERSON> todo", "dbfilter_show": "Mostrar se<PERSON>", "dbfilter_hide": "Ocultar selecciona<PERSON>", "dbfilter_show_title": "Bases de datos a mostrar", "dbfilter_hide_title": "Bases de datos a ocultar", "dbfilter_input": "Ingresar índice de base de datos", "dbfilter_input_tip": "Presione Enter para confirmar", "key_view": "Vista de clave predeterminada", "key_view_tree": "Vista de árbol", "key_view_list": "Vista de lista", "load_size": "<PERSON><PERSON><PERSON> por carga", "mark_color": "Color de marca"}, "alias": {"title": "Alias de base de datos", "db": "Ingresar índice de base de datos", "value": "Ingresar alias de base de datos"}, "ssl": {"title": "SSL/TLS", "enable": "Habilitar SSL/TLS", "allow_insecure": "<PERSON><PERSON><PERSON> ins<PERSON>", "sni": "Nombre de servidor (SNI)", "sni_tip": "(Opcional) Nombre del servidor", "cert_file": "Archivo de clave pública", "key_file": "Archivo de clave privada", "ca_file": "Archivo CA", "cert_file_tip": "Archivo de clave pública en formato PEM (Cert)", "key_file_tip": "Archivo de clave privada en formato PEM (Key)", "ca_file_tip": "Archivo de autoridad de certificación en formato PEM (CA)"}, "ssh": {"enable": "Habilitar túnel SSH", "title": "Túnel SSH", "login_type": "Tipo de inicio de sesión", "pkfile": "Archivo de clave privada", "passphrase": "Frase de contraseña", "addr_tip": "Dirección del servidor SSH", "usr_tip": "Usuario SSH", "pwd_tip": "Contraseña SSH", "pkfile_tip": "Ruta del archivo de clave privada SSH", "passphrase_tip": "(Opcional) Frase de contraseña para la clave privada"}, "sentinel": {"title": "Centinela", "enable": "Como nodo centinela", "master": "Nombre del grupo maestro", "auto_discover": "Descubrimiento automático", "password": "Contraseña del maestro", "username": "Usuario del maestro", "pwd_tip": "(Opcional) Contraseña de autenticación del maestro (Redis > 6.0)", "usr_tip": "(Opcional) Usuario de autenticación del maestro"}, "cluster": {"title": "<PERSON><PERSON><PERSON><PERSON>", "enable": "Como nodo de clúster"}, "proxy": {"title": "Proxy", "type_none": "Sin proxy", "type_system": "Proxy del sistema", "type_custom": "Proxy manual", "host": "Nombre de host", "auth": "Autenticación de proxy", "usr_tip": "Usuario de autenticación de proxy", "pwd_tip": "Contraseña de autenticación de proxy"}}, "group": {"name": "Nombre del grupo", "rename": "Renombrar grupo", "new": "Nuevo grupo"}, "key": {"new": "Nueva clave", "new_name": "Nuevo nombre de clave", "server": "Conexión", "db_index": "Índice de base de datos", "key_expression": "Patrón de <PERSON>", "affected_key": "Claves a<PERSON>adas", "show_affected_key": "Mostrar claves afectadas", "confirm_delete_key": "Confirmar eliminar {num} clave(s)", "direct_delete": "Eliminar el patrón coincidente directamente", "confirm_delete": "Confirmar eliminación", "async_delete": "Ejecución asíncrona", "async_delete_title": "No esperar el resultado", "confirm_flush": "¡Sé lo que estoy haciendo!", "confirm_flush_db": "Confirmar vaciar la base de datos"}, "delete": {"success": "\"{key}\" eliminada", "deleting": "Eliminando", "doing": "Eliminando clave ({index}/{count})", "completed": "Eliminación completada, {success} tuvieron éxito, {fail} fallaron"}, "field": {"new": "Nuevo campo", "new_item": "Nuevo elemento", "conflict_handle": "En conflicto de campo", "overwrite_field": "Sobrescribir", "ignore_field": "<PERSON><PERSON><PERSON>", "insert_type": "Tipo de inserción", "append_item": "Anexar", "prepend_item": "<PERSON><PERSON><PERSON><PERSON>", "enter_key": "Ingresar clave", "enter_value": "Ingresar valor", "enter_field": "Ingresar nombre de campo", "enter_elem": "Ingresar elemento", "enter_member": "Ingresar miembro", "enter_score": "Ingresar puntuación", "element": "Elemento", "reload_when_succ": "Recargar inmediatamente si tiene éxito"}, "filter": {"set_key_filter": "<PERSON><PERSON><PERSON> filt<PERSON> de clave", "filter_pattern": "<PERSON><PERSON><PERSON>", "filter_pattern_tip": "Filtre la lista actual ingresando directamente, y escanee el servidor presionando 'Ingresar'.\n\n* coincide con 0 o más caracteres, ej. 'key*'\n? coincide con un carácter, ej. 'key?'\n[] coincide con un rango, ej. 'key[1-3]'\n\\ escapa caracteres especiales", "exact_match_tip": "Coincidencia exacta", "filter_type_not_support": "El filtrado por tipo no es compatible con Redis 5.x y versiones anteriores"}, "export": {"name": "Exportar datos", "export_expire_title": "Expiración", "export_expire": "Incluir expiración", "export": "Exportar", "save_file": "Ruta de exportación", "save_file_tip": "Seleccionar ruta para guardar archivo exportado", "exporting": "Exportando claves ({index}/{count})", "export_completed": "Exportación completada, {success} tuvieron éxito, {fail} fallaron"}, "import": {"name": "Importar datos", "import_expire_title": "Expiración", "import": "Importar", "reload": "Recargar después de importar", "open_csv_file": "Archivo de importación", "open_csv_file_tip": "Seleccionar archivo a importar", "conflict_handle": "En conflicto de clave", "conflict_overwrite": "Sobrescribir", "conflict_ignore": "<PERSON><PERSON><PERSON>", "ttl_include": "Importar desde archivo", "ttl_ignore": "No establecer", "ttl_custom": "Personalizado", "importing": "Importando claves importadas/sobrescritas:{imported} conflicto/fallas:{conflict}", "import_completed": "Importación completada, {success} tuvieron éxito, {ignored} ignoradas"}, "ttl": {"title": "Actualizar TTL", "title_batch": "Actualizar TTL en lote ({count})", "quick_set": "Configurar <PERSON>", "success": "TTL actualizado para todas las claves"}, "decoder": {"name": "Nuevo decodificador/codificador", "edit_name": "Editar decodificador/codificador", "new": "Nuevo", "decoder": "Decodificador", "encoder": "Codificador", "decoder_name": "Nombre", "auto": "Decodificar automáticamente", "decode_path": "Ruta del decodificador", "encode_path": "Ruta del codificador", "path_help": "<PERSON><PERSON> al ejecutable, o alias de cli como 'sh/php/python'", "args": "Argumentos", "args_help": "Use [VALUE] como marcador de posición para codificar/decodificar contenido. El contenido se agregará al final si no se proporciona marcador de posición."}, "upgrade": {"title": "Nueva versión disponible", "new_version_tip": "Nueva versión {ver} disponible, ¿descargar ahora?", "no_update": "Está actualizado", "download_now": "<PERSON><PERSON><PERSON> ahora", "later": "<PERSON>ás tarde", "skip": "Omitir esta versión"}, "welcome": {"title": "¡Bienvenido a Tiny RDM!", "content": "Con el fin de brindar una mejor experiencia de usuario, Tiny RDM recopila algunos datos anónimos para ayudar a optimizar el software y mejorar la experiencia del usuario. Tenga la seguridad de que esto no involucrará su información de privacidad personal.\n\nSi tiene alguna inquietud, puede desactivar esta función de recopilación de datos en cualquier momento yendo a Preferencias. Si tiene alguna pregunta, no dude en ponerse en contacto con el desarrollador. ¡Espero que Tiny RDM pueda ser un buen asistente para usted!", "accept": "<PERSON><PERSON><PERSON>", "reject": "<PERSON><PERSON><PERSON>"}, "about": {"source": "<PERSON><PERSON><PERSON> fuente", "website": "Sitio web oficial"}}, "menu": {"minimise": "<PERSON><PERSON><PERSON>", "maximise": "Maximizar", "restore": "Restaurar", "close": "<PERSON><PERSON><PERSON>", "preferences": "Preferencias", "help": "<PERSON><PERSON><PERSON>", "user_guide": "Guía de usuario", "check_update": "Buscar actualizaciones...", "report_bug": "Reportar error", "about": "Acerca de"}, "log": {"title": "Registro de lanzamiento", "filter_server": "<PERSON>lt<PERSON> servidor", "filter_keyword": "Filtrar palabra clave", "clean_log": "Limpiar registro", "confirm_clean_log": "Confirmar limp<PERSON> regis<PERSON>", "exec_time": "Tiempo de ejecución", "server": "<PERSON><PERSON><PERSON>", "cmd": "Comand<PERSON>", "cost_time": "Costo", "refresh": "Actualizar"}, "status": {"uptime": "Tiempo activo", "connected_clients": "Clientes", "total_keys": "<PERSON><PERSON><PERSON>", "memory_used": "Memoria", "server_info": "Información del servidor", "activity_status": "Actividad", "act_cmd": "Comandos/Seg", "act_network_input": "Entrada de red", "act_network_output": "Salida de red", "client": {"title": "Lista de clientes", "addr": "Dirección del cliente", "age": "Edad (seg)", "idle": "Inactivo (seg)", "db": "Base de datos"}}, "slog": {"title": "Registro lento", "limit": "Límite", "filter": "Filtrar", "exec_time": "Tiempo", "client": "Cliente", "cmd": "Comand<PERSON>", "cost_time": "Costo"}, "monitor": {"title": "Monitorear comandos", "actions": "Acciones", "warning": "El monitoreo de comandos puede causar bloqueos en el servidor, úselo con precaución en servidores de producción.", "start": "Iniciar", "stop": "Detener", "search": "Buscar", "copy_log": "Copiar registro", "save_log": "Guardar registro", "clean_log": "Limpiar registro", "always_show_last": "Desplazamiento automático al último"}, "pubsub": {"title": "Pub/Sub", "publish": "Publicar", "subscribe": "Suscribir", "unsubscribe": "Cancelar suscripción", "clear": "Lim<PERSON><PERSON>", "time": "Tiempo", "filter": "Filtrar", "channel": "Canal", "message": "Men<PERSON><PERSON>", "receive_message": "Recibidos {total} mensajes", "always_show_last": "Desplazamiento automático al último"}}