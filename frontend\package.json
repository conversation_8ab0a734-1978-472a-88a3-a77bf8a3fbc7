{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"chart.js": "^4.5.0", "copy-text-to-clipboard": "^3.2.0", "dayjs": "^1.11.13", "lodash": "^4.17.21", "monaco-editor": "^0.47.0", "pinia": "^3.0.3", "sass": "^1.89.2", "vue": "^3.5.18", "vue-chartjs": "^5.3.2", "vue-i18n": "^11.1.11", "wcwidth": "^1.0.1", "xterm": "^5.3.0", "xterm-addon-fit": "^0.8.0"}, "devDependencies": {"@vitejs/plugin-vue": "^6.0.0", "naive-ui": "^2.42.0", "prettier": "^3.6.2", "unplugin-auto-import": "^19.3.0", "unplugin-icons": "^22.1.0", "unplugin-vue-components": "^28.8.0", "vite": "^7.0.6"}}