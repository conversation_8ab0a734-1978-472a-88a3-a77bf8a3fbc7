{"name": "日本語", "common": {"confirm": "確認", "cancel": "キャンセル", "success": "成功", "warning": "警告", "error": "エラー", "save": "保存", "update": "更新", "none": "なし", "second": "秒", "minute": "分", "hour": "時間", "day": "日", "unit_day": "日", "unit_hour": "時間", "unit_minute": "分", "unit_second": "秒", "all": "すべて", "key": "キー", "value": "値", "field": "フィールド", "score": "スコア", "index": "位置"}, "preferences": {"name": "設定", "restore_defaults": "デフォルトに戻す", "font_tip": "複数選択可能、インストール済みのフォントがリストにない場合は手動で入力できます", "general": {"name": "一般設定", "theme": "テーマ", "theme_light": "ライトモード", "theme_dark": "ダークモード", "theme_auto": "自動", "language": "言語", "system_lang": "システム言語を使用", "font": "フォント", "font_tip": "フォント名を選択または入力してください", "font_size": "フォントサイズ", "scan_size": "SCANコマンドのデフォルトサイズ", "scan_size_tip": "SCAN/HSCAN/SSCAN/ZSCAN コマンドで1回に返される要素の数", "key_icon_style": "キーアイコンのスタイル", "key_icon_style0": "コンパクトタイプ", "key_icon_style1": "フルネームタイプ", "key_icon_style2": "ドットタイプ", "key_icon_style3": "共通アイコン", "update": "更新", "auto_check_update": "自動でアップデートを確認", "privacy": "プライバシーポリシー", "allow_track": "匿名データの収集を許可する"}, "editor": {"name": "エディター", "show_linenum": "行番号を表示", "show_folding": "コード折りたたみを有効化", "drop_text": "テキストのドラッグ&ドロップを許可", "links": "リンクをサポート"}, "cli": {"name": "コマンドライン", "cursor_style": "カーソルスタイル", "cursor_style_block": "ブロック", "cursor_style_underline": "アンダーライン", "cursor_style_bar": "バー"}, "decoder": {"name": "カスタムデコーダー", "new": "新しいデコーダー", "decoder_name": "名前", "cmd_preview": "プレビュー", "status": "ステータス", "auto_enabled": "自動デコーディングが有効化されました", "help": "ヘルプ"}}, "interface": {"new_conn": "新しい接続を追加", "new_group": "新しいグループを追加", "disconnect_all": "すべての接続を切断", "status": "ステータス", "filter": "フィルター", "sort_conn": "接続を並べ替え", "new_conn_title": "新しい接続", "open_db": "データベースを開く", "close_db": "データベースを閉じる", "filter_key": "キーをフィルター", "disconnect": "切断", "dup_conn": "接続を複製", "remove_conn": "接続を削除", "edit_conn": "接続設定を編集", "edit_conn_group": "グループを編集", "rename_conn_group": "グループ名を変更", "remove_conn_group": "グループを削除", "import_conn": "接続をインポート...", "export_conn": "接続をエクスポート...", "ttl": "TTL", "forever": "永久", "rename_key": "キー名を変更", "delete_key": "キーを削除", "batch_delete_key": "キーを一括削除", "import_key": "キーをインポート", "flush_db": "データベースをフラッシュ", "check_mode": "チェックモード", "quit_check_mode": "チェックモードを終了", "delete_checked": "チェックされたものを削除", "export_checked": "チェックされたものをエクスポート", "ttl_checked": "チェックされたもののTTLを更新", "copy_value": "値をコピー", "edit_value": "値を編集", "save_update": "変更を保存", "score_filter_tip": "以下の演算子を使って範囲をフィルターできます\n=：等しい\n!=：等しくない\n>：より大きい\n>=：以上\n<：より小さい\n<=：以下\n例）スコアが3よりも大きいものを検索する場合は、>3と入力します", "add_row": "行を挿入", "edit_row": "行を編集", "delete_row": "行を削除", "fullscreen": "全画面表示", "offscreen": "全画面表示を終了", "pin_edit": "編集ボックスを固定（保存後も閉じない）", "unpin_edit": "ピン留めを解除", "search": "検索", "full_search": "全文検索", "full_search_result": "コンテンツが'{pattern}'にマッチしました", "filter_field": "フィールドをフィルター", "filter_value": "値をフィルター", "length": "長さ", "entries": "エントリ", "memory_usage": "メモリ使用量", "text_align_left": "左揃え", "text_align_center": "中央揃え", "view_as": "表示形式", "decode_with": "デコード/解凍", "custom_decoder": "新しいカスタムデコーダー", "reload": "再読み込み", "reload_disable": "すべて読み込んだ後に再読み込みできます", "auto_refresh": "自動更新", "refresh_interval": "更新間隔", "open_connection": "接続を開く", "copy_path": "パスをコピー", "copy_key": "キーをコピー", "save_value_succ": "値を保存しました！", "copy_succ": "クリップボードにコピーしました！", "binary_key": "バイナリキー名", "remove_key": "キーを削除", "new_key": "新しいキー", "load_more": "キーをさらに読み込む", "load_all": "残りのすべてのキーを読み込む", "load_more_entries": "さらに読み込む", "load_all_entries": "すべて読み込む", "more_action": "その他の操作", "nonexist_tab_content": "選択したキーが存在しないか、キーが選択されていません。更新後に再試行してください。", "empty_server_content": "左のパネルから接続を選択して開いてください", "empty_server_list": "Redisサーバーが追加されていません", "action": "アクション", "type": "タイプ", "cli_welcome": "<PERSON> RDMのRedisコンソールへようこそ", "retrieving_version": "新しいバージョンを確認しています", "sub_tab": {"status": "ステータス", "key_detail": "キーの詳細", "cli": "コンソール", "slow_log": "スロー ログ", "cmd_monitor": "コマンドのモニタリング", "pub_message": "パブ/サブ"}}, "ribbon": {"server": "サーバー", "browser": "データ ブラウザ", "log": "ログ", "wechat_official": "Wechat 公式アカウント", "follow_x": "私の 𝕏 をフォローする", "github": "<PERSON><PERSON><PERSON>"}, "dialogue": {"close_confirm": "この接続（{name}）を閉じますか？", "edit_close_confirm": "編集する前に関連する接続を閉じる必要があります。続行しますか？", "opening_connection": "接続を開いています...", "interrupt_connection": "キャンセル", "remove_tip": "{type} \"{name}\" が削除されます", "remove_group_tip": "グループ \"{name}\" とそのすべての接続が削除されます", "rename_binary_key_fail": "バイナリキーの名前は変更できません", "handle_succ": "成功しました！", "handle_cancel": "操作がキャンセルされました。", "reload_succ": "再読み込みしました！", "field_required": "この項目は必須です", "spec_field_required": "\"{key}\" は必須です", "illegal_characters": "不正な文字が含まれています", "connection": {"new_title": "新しい接続", "edit_title": "接続を編集", "general": "一般", "no_group": "グループなし", "group": "グループ", "conn_name": "名前", "addr": "アドレス", "usr": "ユーザー名", "pwd": "パスワード", "name_tip": "接続名", "addr_tip": "Redisサーバーのアドレス", "sock_tip": "Redisのunixソケットファイル", "usr_tip": "（オプション）認証ユーザー名", "pwd_tip": "（オプション）認証パスワード (Redis > 6.0)", "test": "接続をテスト", "test_succ": "Redisサーバーに正常に接続しました", "test_fail": "接続に失敗しました", "parse_url_clipboard": "クリップボードからURLを解析", "parse_pass": "RedisのURLを解析しました: {url}", "parse_fail": "RedisのURLを解析できませんでした: {reason}", "advn": {"title": "高度な設定", "filter": "デフォルトのキーフィルター", "filter_tip": "読み込むキーのパターン", "separator": "キーセパレーター", "separator_tip": "キーパスのセグメントの区切り文字", "conn_timeout": "接続タイムアウト", "exec_timeout": "実行タイムアウト", "dbfilter_type": "データベースフィルター", "dbfilter_all": "すべて表示", "dbfilter_show": "選択したものを表示", "dbfilter_hide": "選択したものを非表示", "dbfilter_show_title": "表示するデータベース", "dbfilter_hide_title": "非表示にするデータベース", "dbfilter_input": "データベースインデックスを入力", "dbfilter_input_tip": "Enterキーで確定", "key_view": "デフォルトのキービュー", "key_view_tree": "ツリービュー", "key_view_list": "リストビュー", "load_size": "1回の読み込みキー数", "mark_color": "マーク色"}, "alias": {"title": "データベースエイリアス", "db": "データベースインデックスを入力", "value": "エイリアスを入力"}, "ssl": {"title": "SSL/TLS", "enable": "SSL/TLSを有効化", "allow_insecure": "安全でない接続を許可", "sni": "サーバー名(SNI)", "sni_tip": "（オプション）サーバー名", "cert_file": "公開鍵ファイル", "key_file": "秘密鍵ファイル", "ca_file": "CAファイル", "cert_file_tip": "PEM形式の公開鍵ファイル(Cert)", "key_file_tip": "PEM形式の秘密鍵ファイル(Key)", "ca_file_tip": "PEM形式のCA証明書ファイル(CA)"}, "ssh": {"enable": "SSHトンネルを有効化", "title": "SSHトンネル", "login_type": "ログイン方式", "pkfile": "秘密鍵ファイル", "passphrase": "パスフレーズ", "addr_tip": "SSHサーバーのアドレス", "usr_tip": "SSHユーザー名", "pwd_tip": "SSHパスワード", "pkfile_tip": "SSHの秘密鍵ファイルのパス", "passphrase_tip": "（オプション）秘密鍵のパスフレーズ"}, "sentinel": {"title": "センチネルモード", "enable": "センチネルノードとして", "master": "マスターグループ名", "auto_discover": "自動検出", "password": "マスターパスワード", "username": "マスターユーザー名", "pwd_tip": "（オプション）マスター認証パスワード (Redis > 6.0)", "usr_tip": "（オプション）マスター認証,ユーザー名"}, "cluster": {"title": "クラスターモード", "enable": "クラスターノードとして"}, "proxy": {"title": "プロキシ", "type_none": "プロキシなし", "type_system": "システムプロキシ設定を使用", "type_custom": "手動でプロキシを設定", "host": "ホスト名", "auth": "プロキシ認証を使用", "usr_tip": "プロキシ認証ユーザー名", "pwd_tip": "プロキシ認証パスワード"}}, "group": {"name": "グループ名", "rename": "グループ名を変更", "new": "新しいグループ"}, "key": {"new": "新しいキー", "new_name": "新しいキー名", "server": "接続", "db_index": "データベースインデックス", "key_expression": "キーパターン", "affected_key": "影響を受けるキー", "show_affected_key": "影響を受けるキーを表示", "confirm_delete_key": "{num}個のキーを削除することを確認", "direct_delete": "一致するパターンを直接削除", "confirm_delete": "削除を確認", "async_delete": "非同期実行", "async_delete_title": "結果を待たない", "confirm_flush": "自分が実行しようとしている操作を理解しています！", "confirm_flush_db": "データベースをフラッシュすることを確認"}, "delete": {"success": "\"{key}\" を削除しました", "deleting": "削除中", "doing": "キーを削除中 ({index}/{count})", "completed": "削除が完了しました。成功: {success}個、失敗: {fail}個"}, "field": {"new": "新しいフィールド", "new_item": "新しい項目", "conflict_handle": "フィールドが競合した場合", "overwrite_field": "上書き", "ignore_field": "無視", "insert_type": "挿入タイプ", "append_item": "末尾に追加", "prepend_item": "先頭に挿入", "enter_key": "キー名を入力", "enter_value": "値を入力", "enter_field": "フィールド名を入力", "enter_elem": "新しい要素を入力", "enter_member": "メンバーを入力", "enter_score": "スコアを入力", "element": "要素", "reload_when_succ": "成功したら即座に再読み込み"}, "filter": {"set_key_filter": "キーフィルターを設定", "filter_pattern": "パターン", "filter_pattern_tip": "直接入力して現在のリストをフィルタリングし、Enterキーを押すとサーバーをスキャンできます。\n\n*：0文字以上にマッチ。例：\"key*\"は\"key\"で始まるすべてのキーにマッチ\n?：1文字にマッチ。例：\"key?\"は\"key1\"、\"key2\"にマッチ\n[ ]：指定範囲の1文字にマッチ。例：\"key[1-3]\"は\"key1\"、\"key2\"、\"key3\"にマッチ\n\\：エスケープ文字。*、?、[、]をリテラルとして解釈したい場合は\"\\ \"をつける", "exact_match_tip": "完全一致", "filter_type_not_support": "タイプフィルタリングは、Redis 5.x 以前のバージョンには対応していません"}, "export": {"name": "データをエクスポート", "export_expire_title": "有効期限", "export_expire": "有効期限を含める", "export": "エクスポート", "save_file": "エクスポート先", "save_file_tip": "エクスポートファイルの保存先を選択", "exporting": "キーをエクスポート中 ({index}/{count})", "export_completed": "エクスポートが完了しました。成功: {success}個、失敗: {fail}個"}, "import": {"name": "データをインポート", "import_expire_title": "有効期限", "import": "インポート", "reload": "インポート後に再読み込み", "open_csv_file": "インポートファイル", "open_csv_file_tip": "インポートするファイルを選択", "conflict_handle": "キーが競合した場合", "conflict_overwrite": "上書き", "conflict_ignore": "無視", "ttl_include": "ファイルから読み込む", "ttl_ignore": "設定しない", "ttl_custom": "カスタム", "importing": "キーをインポート中 インポート/上書き:{imported} 競合/失敗:{conflict}", "import_completed": "インポートが完了しました。成功: {success}個、無視: {ignored}個"}, "ttl": {"title": "TTLを更新", "title_batch": "TTLを一括更新 ({count})", "quick_set": "クイック設定", "success": "すべてのキーのTTLが更新されました"}, "decoder": {"name": "新しいデコーダー/エンコーダー", "edit_name": "デコーダー/エンコーダーを編集", "new": "新規", "decoder": "デコーダー", "encoder": "エンコーダー", "decoder_name": "名前", "auto": "自動デコード", "decode_path": "デコーダーのパス", "encode_path": "エンコーダーのパス", "path_help": "実行ファイルのパスか、'sh/php/python'のようなCLIエイリアス", "args": "引数", "args_help": "エンコード/デコードするコンテンツの場所には[VALUE]を使ってください。プレースホルダーを指定しない場合は、コンテンツが最後に付加されます。"}, "upgrade": {"title": "新しいバージョンが利用可能です", "new_version_tip": "新しいバージョン{ver}が利用可能です。今すぐダウンロードしますか？", "no_update": "最新バージョンです", "download_now": "今すぐダウンロード", "later": "後で", "skip": "このバージョンをスキップ"}, "welcome": {"title": "Tiny RDMをご利用いただきありがとうございます!", "content": "ユーザーエクスペリエンスを改善するために、Tiny RDMは一部の匿名データを収集し、ソフトウェアの最適化とユーザーエクスペリエンスの向上に役立てています。個人プライバシー情報は含まれませんのでご安心ください。\n\nご不安な点がありましたら、いつでも「設定」から当該機能をオフにすることができます。ご不明な点がございましたら、開発者までお問い合わせください。Tiny RDMがお役に立てることを願っております。", "accept": "改善の支援", "reject": "拒否する"}, "about": {"source": "ソースコード", "website": "公式ウェブサイト"}}, "menu": {"minimise": "最小化", "maximise": "最大化", "restore": "元に戻す", "close": "閉じる", "preferences": "設定", "help": "ヘルプ", "user_guide": "ユーザーガイド", "check_update": "アップデートを確認...", "report_bug": "バグを報告", "about": "このソフトについて"}, "log": {"title": "実行ログ", "filter_server": "サーバーをフィルター", "filter_keyword": "キーワードでフィルター", "clean_log": "ログをクリア", "confirm_clean_log": "実行ログをクリアしてよろしいですか？", "exec_time": "実行時間", "server": "サーバー", "cmd": "コマンド", "cost_time": "時間", "refresh": "すぐに更新"}, "status": {"uptime": "稼働時間", "connected_clients": "接続クライアント数", "total_keys": "合計キー数", "memory_used": "メモリ使用量", "server_info": "サーバー情報", "activity_status": "アクティビティ状況", "act_cmd": "コマンド実行数(/秒)", "act_network_input": "ネットワーク入力", "act_network_output": "ネットワーク出力", "client": {"title": "クライアント一覧", "addr": "クライアントアドレス", "age": "接続時間（秒）", "idle": "アイドル時間（秒）", "db": "データベース"}}, "slog": {"title": "スローログ", "limit": "上限", "filter": "フィルター", "exec_time": "実行時間", "client": "クライアント", "cmd": "コマンド", "cost_time": "時間"}, "monitor": {"title": "コマンドのモニタリング", "actions": "アクション", "warning": "コマンドのモニタリングは、サーバーをブロックする可能性があるため、運用環境のサーバーでは注意して使用してください。", "start": "開始", "stop": "停止", "search": "検索", "copy_log": "ログをコピー", "save_log": "ログを保存", "clean_log": "ログをクリア", "always_show_last": "最新に自動スクロール"}, "pubsub": {"title": "パブ/サブ", "publish": "公開", "subscribe": "購読を開始", "unsubscribe": "購読を解除", "clear": "メッセージをクリア", "time": "時間", "filter": "フィルター", "channel": "チャンネル", "message": "メッセージ", "receive_message": "{total}件のメッセージを受信しました", "always_show_last": "最新に自動スクロール"}}