name: Release Windows App

on:
  release:
    types: [ published ]
  workflow_dispatch:
    inputs:
      tag:
        description: 'Version tag'
        required: true
        default: '1.0.0'

jobs:
  release:
    name: Release Windows App
    runs-on: windows-latest
    strategy:
      matrix:
        platform:
          - windows/amd64
          - windows/arm64
    steps:
      - name: Checkout source code
        uses: actions/checkout@v3

      - name: Normalise platform tag
        id: normalise_platform
        shell: bash
        run: |
          tag=$(echo ${{ matrix.platform }} | sed -e 's/\//_/g' -e 's/amd64/x64/g')
          echo "tag=$tag" >> "$GITHUB_OUTPUT"

      - name: Normalise platform name
        id: normalise_platform_name
        shell: bash
        run: |
          pname=$(echo "${{ matrix.platform }}" | sed 's/windows\///g')
          echo "pname=$pname" >> "$GITHUB_OUTPUT"

      - name: Normalise version tag
        id: normalise_version
        shell: bash
        run: |
          if [ "${{ github.event.release.tag_name }}" == "" ]; then
            version=$(echo ${{ github.event.inputs.tag }} | sed -e 's/v//g')
            echo "version=$version" >> "$GITHUB_OUTPUT"
          else
            version=$(echo ${{ github.event.release.tag_name }} | sed -e 's/v//g')
            echo "version=$version" >> "$GITHUB_OUTPUT"
          fi

      - name: Setup Go
        uses: actions/setup-go@v4
        with:
          go-version: stable

      - name: Install chocolatey
        uses: crazy-max/ghaction-chocolatey@v2
        with:
          args: install nsis jq

      - name: Install wails
        shell: bash
        run: go install github.com/wailsapp/wails/v2/cmd/wails@latest

      - name: Setup Node
        uses: actions/setup-node@v3
        with:
          node-version: 20

      - name: Build frontend assets
        shell: bash
        run: |
          npm install -g npm@9
          jq '.info.productVersion = "${{ steps.normalise_version.outputs.version }}"' wails.json > tmp.json
          mv tmp.json wails.json
          cd frontend
          jq '.version = "${{ steps.normalise_version.outputs.version }}"' package.json > tmp.json
          mv tmp.json package.json
          npm install

      - name: Build Windows portable app
        shell: bash
        run: |
          CGO_ENABLED=1 wails build -clean -platform ${{ matrix.platform }} \
          -webview2 embed \
          -ldflags "-X main.version=v${{ steps.normalise_version.outputs.version }} -X main.gaMeasurementID=${{ secrets.GA_MEASUREMENT_ID }} -X main.gaSecretKey=${{ secrets.WINDOWS_GA_SECRET }}"

      - name: Compress portable binary
        working-directory: ./build/bin
        run: Compress-Archive "Tiny RDM.exe" "TinyRDM_Portable_${{ steps.normalise_version.outputs.version }}_${{ steps.normalise_platform.outputs.tag }}.zip"

      - name: Upload release asset (Portable)
        uses: softprops/action-gh-release@v1
        with:
          tag_name: v${{ steps.normalise_version.outputs.version }}
          files: ./build/bin/TinyRDM_Portable_${{ steps.normalise_version.outputs.version }}_${{ steps.normalise_platform.outputs.tag }}.zip
          token: ${{ secrets.GITHUB_TOKEN }}

      - name: Build Windows NSIS installer
        shell: bash
        run: |
          CGO_ENABLED=1 wails build -clean -platform ${{ matrix.platform }} \
          -nsis -webview2 embed \
          -ldflags "-X main.version=v${{ steps.normalise_version.outputs.version }}"

      - name: Codesign Windows NSIS installer
        shell: powershell
        working-directory: ./build/bin
        run: |
          echo "Creating certificate file"
          New-Item -ItemType directory -Path certificate
          Set-Content -Path certificate\certificate.txt -Value '${{ secrets.WIN_SIGNING_CERT }}'
          certutil -decode certificate\certificate.txt certificate\certificate.pfx
          echo "Signing TinyRDM installer"
          & 'C:/Program Files (x86)/Windows Kits/10/bin/10.0.17763.0/x86/signtool.exe' sign /td sha256 /tr http://timestamp.sectigo.com/?td=sha256 /f certificate\certificate.pfx /p '${{ secrets.WIN_SIGNING_CERT_PASSWORD }}' 'TinyRDM-${{ steps.normalise_platform_name.outputs.pname }}-installer.exe'

      - name: Rename installer
        working-directory: ./build/bin
        run: Rename-Item -Path "TinyRDM-${{ steps.normalise_platform_name.outputs.pname }}-installer.exe" -NewName "TinyRDM_Setup_${{ steps.normalise_version.outputs.version }}_${{ steps.normalise_platform.outputs.tag }}.exe"

      - name: Upload release asset (Installer)
        uses: softprops/action-gh-release@v1
        with:
          tag_name: v${{ steps.normalise_version.outputs.version }}
          files: ./build/bin/TinyRDM_Setup_${{ steps.normalise_version.outputs.version }}_${{ steps.normalise_platform.outputs.tag }}.exe
          token: ${{ secrets.GITHUB_TOKEN }}
