{"name": "简体中文", "common": {"confirm": "确认", "cancel": "取消", "success": "成功", "warning": "警告", "error": "错误", "save": "保存", "update": "更新", "none": "无", "second": "秒", "minute": "分", "hour": "小时", "day": "天", "unit_day": "天", "unit_hour": "小时", "unit_minute": "分钟", "unit_second": "秒", "all": "全部", "key": "键", "value": "值", "field": "字段", "score": "分值", "index": "位置"}, "preferences": {"name": "偏好设置", "restore_defaults": "重置为默认", "font_tip": "支持多选，如列表没有已安装的字体，可自行手动输入", "general": {"name": "常规配置", "theme": "主题", "theme_light": "浅色", "theme_dark": "深色", "theme_auto": "自动", "language": "语言", "system_lang": "使用系统语言", "font": "字体", "font_tip": "请选择或手动输入字体名", "font_size": "字体尺寸", "scan_size": "SCAN命令默认数量", "scan_size_tip": "SCAN/HSCAN/SSCAN/ZSCAN 命令每次返回数量", "key_icon_style": "键图标样式", "key_icon_style0": "紧凑类型", "key_icon_style1": "全称类型", "key_icon_style2": "圆点类型", "key_icon_style3": "通用图标", "update": "更新", "auto_check_update": "自动检查更新", "privacy": "隐私策略", "allow_track": "允许收集匿名数据"}, "editor": {"name": "编辑器", "show_linenum": "显示行号", "show_folding": "启用代码折叠", "drop_text": "允许拖放文本", "links": "支持链接跳转"}, "cli": {"name": "命令行", "cursor_style": "光标样式", "cursor_style_block": "方块", "cursor_style_underline": "下划线", "cursor_style_bar": "竖线"}, "decoder": {"name": "自定义解码", "new": "新增自定义解码", "decoder_name": "解码器名称", "cmd_preview": "命令预览", "status": "状态", "auto_enabled": "已加入自动解码", "help": "帮助"}}, "interface": {"new_conn": "添加新连接", "new_group": "添加新分组", "disconnect_all": "断开所有连接", "status": "状态", "filter": "筛选", "sort_conn": "调整连接顺序", "new_conn_title": "新建连接", "open_db": "打开数据库", "close_db": "关闭数据库", "filter_key": "过滤键", "disconnect": "断开连接", "dup_conn": "复制连接", "remove_conn": "删除连接", "edit_conn": "编辑连接配置", "edit_conn_group": "编辑分组", "rename_conn_group": "重命名分组", "remove_conn_group": "删除分组", "import_conn": "导入连接...", "export_conn": "导出连接...", "ttl": "TTL", "forever": "永久", "rename_key": "重命名键", "delete_key": "删除键", "batch_delete_key": "批量删除键", "import_key": "导入数据", "flush_db": "清空数据库", "check_mode": "勾选模式", "quit_check_mode": "退出勾选模式", "delete_checked": "删除所选项", "export_checked": "导出所选项", "ttl_checked": "为所选项更新TTL", "copy_value": "复制值", "edit_value": "修改值", "save_update": "保存修改", "score_filter_tip": "支持如下运算符比较匹配范围\n＝：等于\n!=：不等于\n>：大于\n<：小于\n>=：大于等于\n<=：小于等于\n如查询分值大于3的结果，则输入：>3", "add_row": "插入行", "edit_row": "编辑行", "delete_row": "删除行", "fullscreen": "全屏显示", "offscreen": "退出全屏显示", "pin_edit": "固定编辑框（保存后不关闭）", "unpin_edit": "取消固定", "search": "搜索", "full_search": "全文匹配", "full_search_result": "内容已匹配为 {pattern}", "filter_field": "筛选字段", "filter_value": "筛选值", "length": "长度", "entries": "条目", "memory_usage": "内存占用", "text_align_left": "文本居左", "text_align_center": "文本居中", "view_as": "查看方式", "decode_with": "解码/解压方式", "custom_decoder": "添加自定义解码", "reload": "重新载入", "reload_disable": "全量加载后可重新载入", "auto_refresh": "自动刷新", "refresh_interval": "刷新间隔", "open_connection": "打开连接", "copy_path": "复制路径", "copy_key": "复制键名", "save_value_succ": "已保存值", "copy_succ": "已复制到剪切板", "binary_key": "二进制键名", "remove_key": "删除键", "new_key": "添加新键", "load_more": "加载更多键", "load_all": "加载剩余所有键", "load_more_entries": "加载更多", "load_all_entries": "加载全部", "more_action": "更多操作", "nonexist_tab_content": "所选键不存在或未选中任何键，请尝试刷新后重试", "empty_server_content": "可以从左边选择并打开连接", "empty_server_list": "还没添加Redis服务器", "action": "操作", "type": "类型", "cli_welcome": "欢迎使用Tiny RDM的Redis命令行控制台", "retrieving_version": "正在检索新版本", "sub_tab": {"status": "状态", "key_detail": "键详情", "cli": "命令行", "slow_log": "慢日志", "cmd_monitor": "监控命令", "pub_message": "发布/订阅"}}, "ribbon": {"server": "服务器", "browser": "数据浏览", "log": "日志", "wechat_official": "微信公众号", "follow_x": "关注我的𝕏", "github": "<PERSON><PERSON><PERSON>"}, "dialogue": {"close_confirm": "是否关闭此连接（{name}）", "edit_close_confirm": "编辑前需要关闭相关连接，是否继续", "opening_connection": "正在打开连接...", "interrupt_connection": "中断连接", "remove_tip": "{type} \"{name}\" 将会被删除", "remove_group_tip": "分组 \"{name}\"及其所有连接将会被删除", "rename_binary_key_fail": "不支持重命名二进制键名", "handle_succ": "操作成功", "handle_cancel": "操作已取消", "reload_succ": "已重新载入", "field_required": "此项不能为空", "spec_field_required": "{key} 不能为空", "illegal_characters": "包含非法字符", "connection": {"new_title": "新建连接", "edit_title": "编辑连接", "general": "常规配置", "no_group": "无分组", "group": "分组", "conn_name": "连接名", "addr": "连接地址", "usr": "用户名", "pwd": "密码", "name_tip": "连接名", "addr_tip": "Redis服务地址", "sock_tip": "Redis套接字文件", "usr_tip": "(可选)Redis服务授权用户名", "pwd_tip": "(可选)Redis服务授权密码 (Redis > 6.0)", "test": "测试连接", "test_succ": "成功连接到Redis服务器", "test_fail": "连接失败", "parse_url_clipboard": "解析剪切板中的URL", "parse_pass": "解析Redis URL完成: {url}", "parse_fail": "解析Redis URL失败: {reason}", "advn": {"title": "高级配置", "filter": "默认键过滤表达式", "filter_tip": "需要加载的键名表达式", "separator": "键分隔符", "separator_tip": "键名路径分隔符", "conn_timeout": "连接超时", "exec_timeout": "执行超时", "dbfilter_type": "数据库过滤方式", "dbfilter_all": "显示所有", "dbfilter_show": "显示指定", "dbfilter_hide": "隐藏指定", "dbfilter_show_title": "需要显示的数据库", "dbfilter_hide_title": "需要隐藏的数据库", "dbfilter_input": "输入数据库索引", "dbfilter_input_tip": "按回车确认", "key_view": "默认键视图", "key_view_tree": "树形列表", "key_view_list": "平铺列表", "load_size": "单次加载键数量", "mark_color": "标记颜色"}, "alias": {"title": "数据库别名", "db": "输入数据库索引", "value": "输入别名"}, "ssl": {"title": "SSL/TLS", "enable": "启用SSL", "allow_insecure": "允许不安全连接", "sni": "服务器名(SNI)", "sni_tip": "(可选)服务器名", "cert_file": "公钥文件", "key_file": "私钥文件", "ca_file": "授权文件", "cert_file_tip": "PEM格式公钥文件(Cert)", "key_file_tip": "PEM格式私钥文件(Key)", "ca_file_tip": "PEM格式授权文件(CA)"}, "ssh": {"enable": "启用SSH隧道", "title": "SSH隧道", "login_type": "登录类型", "pkfile": "私钥文件", "passphrase": "私钥密码", "addr_tip": "SSH地址", "usr_tip": "SSH登录用户名", "pwd_tip": "SSH登录密码", "pkfile_tip": "SSH私钥文件路径", "passphrase_tip": "(可选)SSH私钥密码"}, "sentinel": {"title": "哨兵模式", "enable": "当前为哨兵节点", "master": "主节点组名", "auto_discover": "自动查询组名", "password": "主节点密码", "username": "主节点用户名", "pwd_tip": "(可选)主节点服务授权密码 (Redis > 6.0)", "usr_tip": "(可选)主节点服务授权用户名"}, "cluster": {"title": "集群模式", "enable": "当前为集群节点"}, "proxy": {"title": "网络代理", "type_none": "不使用代理", "type_system": "使用系统代理设置", "type_custom": "手动配置代理", "host": "主机名", "auth": "使用身份验证", "usr_tip": "代理授权用户名", "pwd_tip": "代理授权密码"}}, "group": {"name": "分组名", "rename": "重命名分组", "new": "添加新分组"}, "key": {"new": "添加新键", "new_name": "新键名", "server": "所属连接", "db_index": "数据库编号", "key_expression": "键名表达式", "affected_key": "受影响的键名", "show_affected_key": "查看受影响的键名", "confirm_delete_key": "确认删除{num}个键", "direct_delete": "直接匹配删除", "confirm_delete": "确认删除", "async_delete": "异步执行", "async_delete_title": "不等待操作结果", "confirm_flush": "我知道我正在执行的操作！", "confirm_flush_db": "确认清空数据库"}, "delete": {"success": "{key} 已被删除", "deleting": "正在删除", "doing": "正在删除键({index}/{count})", "completed": "已完成删除操作，成功{success}个，失败{fail}个"}, "field": {"new": "添加新字段", "new_item": "添加新元素", "conflict_handle": "字段冲突处理", "overwrite_field": "覆盖", "ignore_field": "忽略", "insert_type": "插入类型", "append_item": "尾部追加", "prepend_item": "插入头部", "enter_key": "输入键名", "enter_value": "输入值", "enter_field": "输入字段名", "enter_elem": "输入新元素", "enter_member": "输入成员", "enter_score": "输入分值", "element": "元素", "reload_when_succ": "操作成功后立即重新加载"}, "filter": {"set_key_filter": "设置键过滤器", "filter_pattern": "过滤表达式", "filter_pattern_tip": "直接输入筛选当前列表，回车后可对服务器进行扫描。\n\n *：匹配零个或多个字符。例如：\"key*\"匹配到以\"key\"开头的所有键\n?：匹配单个字符。例如：\"key?\"匹配\"key1\"、\"key2\"\n[ ]：匹配指定范围内的单个字符。例如：\"key[1-3]\"可以匹配类似于 \"key1\"、\"key2\"、\"key3\" 的键\n\\：转义字符。如果想要匹配 *、?、[、或]，需要使用反斜杠\"\\\"进行转义", "exact_match_tip": "完全匹配", "filter_type_not_support": "类型筛选不支持 Redis 5.x 及以下版本"}, "export": {"name": "导出数据", "export_expire_title": "过期时间", "export_expire": "同时导出过期时间", "export": "确认导出", "save_file": "导出路径", "save_file_tip": "选择导出文件保存路径", "exporting": "正在导出键({index}/{count})", "export_completed": "已完成导出操作，成功{success}个，失败{fail}个"}, "import": {"name": "导入数据", "import_expire_title": "过期时间", "reload": "导入完成后重新载入", "import": "确认导入", "open_csv_file": "导入文件路径", "open_csv_file_tip": "选择需要导入的文件", "conflict_handle": "键冲突处理", "conflict_overwrite": "覆盖", "conflict_ignore": "忽略", "ttl_include": "尝试导入", "ttl_ignore": "不设置", "ttl_custom": "自定义", "importing": "正在导入数据 已导入/覆盖:{imported} 冲突/失败:{conflict}", "import_completed": "已完成导入操作，成功{success}个，忽略{ignored}个"}, "ttl": {"title": "设置键存活时间", "title_batch": "批量设置键存活时间({count})", "quick_set": "快捷设置", "success": "已全部更新TTL"}, "decoder": {"name": "新增解码/编码器", "edit_name": "编辑解码/编码器", "new": "新增", "decoder": "解码器", "encoder": "编码器", "decoder_name": "解码器名称", "auto": "自动解码", "decode_path": "解码器执行路径", "encode_path": "编码器执行路径", "path_help": "执行文件路径，也可以直接填写命令行接口，如sh/php/python", "args": "运行参数", "args_help": "使用[VALUE]代替编码/解码内容占位符，如果不填内容占位则默认放最后"}, "upgrade": {"title": "有可用新版本", "new_version_tip": "新版本（{ver}），是否立即下载", "no_update": "当前已是最新版", "download_now": "立即下载", "later": "稍后提醒我", "skip": "忽略本次更新"}, "welcome": {"title": "欢迎使用Tiny RDM！", "content": "为了提供更好的用户体验，Tiny RDM会收集一些匿名的数据，以帮助优化软件和改进用户体验，请放心这不会涉及到您的个人隐私信息。\n\n如果您对此有任何顾虑，可以随时前往\"偏好设置\"中关闭此项数据收集功能。有任何问题可联系开发者，希望Tiny RDM可以成为您的好帮手！", "accept": "帮助改进", "reject": "不接受"}, "about": {"source": "源码地址", "website": "官方网站"}}, "menu": {"minimise": "最小化", "maximise": "最大化", "restore": "还原", "close": "关闭", "preferences": "偏好设置", "help": "帮助", "user_guide": "使用指南", "check_update": "检查更新...", "report_bug": "报告错误", "about": "关于"}, "log": {"title": "运行日志", "filter_server": "筛选服务器", "filter_keyword": "筛选关键字", "clean_log": "清空运行日志", "confirm_clean_log": "确定清空运行日志", "exec_time": "执行时间", "server": "服务器", "cmd": "命令", "cost_time": "耗时", "refresh": "立即刷新"}, "status": {"uptime": "运行时间", "connected_clients": "已连客户端", "total_keys": "键总数", "memory_used": "内存使用", "server_info": "状态信息", "activity_status": "活动状态", "act_cmd": "命令执行数/秒", "act_network_input": "网络输入", "act_network_output": "网络输出", "client": {"title": "所有客户端列表", "addr": "客户端地址", "age": "连接时长(秒)", "idle": "空闲时长(秒)", "db": "数据库"}}, "slog": {"title": "慢日志", "limit": "条数", "filter": "筛选", "exec_time": "执行时间", "client": "客户端", "cmd": "命令", "cost_time": "耗时"}, "monitor": {"title": "监控命令", "actions": "操作", "warning": "命令监控可能会造成服务端堵塞，请谨慎在生产环境的服务器使用", "start": "开启监控", "stop": "停止监控", "search": "搜索", "copy_log": "复制日志", "save_log": "保存日志", "clean_log": "清空日志", "always_show_last": "自动滚到最新"}, "pubsub": {"title": "发布订阅", "publish": "发布", "subscribe": "开启订阅", "unsubscribe": "取消订阅", "clear": "清空消息", "time": "时间", "filter": "筛选", "channel": "频道", "message": "消息", "receive_message": "已接收消息 {total} 条", "always_show_last": "自动滚到最新"}}