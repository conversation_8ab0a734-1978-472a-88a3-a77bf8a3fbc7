{"$schema": "https://wails.io/schemas/config.v2.json", "name": "tinyrdm", "outputfilename": "Tiny RDM", "frontend:install": "npm install", "frontend:build": "npm run build", "frontend:dev:watcher": "npm run dev", "frontend:dev:serverUrl": "auto", "author": {"name": "tiny-craft", "email": "<EMAIL>"}, "info": {"companyName": "Tiny Craft", "productName": "Tiny RDM", "productVersion": "1.0.0", "copyright": "Copyright © 2025", "comments": "Tiny Redis Desktop Manager"}}