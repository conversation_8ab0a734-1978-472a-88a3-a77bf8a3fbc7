#!/bin/bash
clear
BLACK="\033[0;30m"
DARK_GRAY="\033[1;30m"
BLUE="\033[0;34m"
LIGHT_BLUE="\033[1;34m"
GREEN="\033[0;32m"
LIGHT_GREEN="\033[1;32m"
CYAN="\033[0;36m"
LIGHT_CYAN="\033[1;36m"
RED="\033[0;31m"
LIGHT_RED="\033[1;31m"
PURPLE="\033[0;35m"
LIGHT_PURPLE="\033[1;35m"
BROWN="\033[0;33m"
YELLOW="\033[0;33m"
LIGHT_GRAY="\033[0;37m"
WHITE="\033[1;37m"
NC="\033[0m"

parentPath=$( cd "$(dirname "${BASH_SOURCE[0]}")" ; pwd -P )
cd "$parentPath"
appPath=$( find "$parentPath" -name '*.app' -maxdepth 1)
appName=${appPath##*/}
appBashName=${appName// /\ }
appDIR="/Applications/${appBashName}"
echo -e "『${appBashName} 提示已损坏，无法打开/ 来自身份不明的开发者』等问题修复工具"
echo ""
# 未安装APP时提醒安装，已安装绕过公证
if [ ! -d "$appDIR" ];then
  echo ""
  echo -e "执行结果：${RED}您还未安装 ${appBashName} ，请先安装${NC}"
  else
  # 绕过公证
  echo -e "${YELLOW}请输入开机密码，输入完成后按下回车键（输入过程中密码是看不见的）${NC}"
  sudo spctl --master-disable
  sudo xattr -rd com.apple.quarantine /Applications/"$appBashName"
  sudo xattr -rc /Applications/"$appBashName"
  sudo codesign --sign - --force --deep /Applications/"$appBashName"
  echo -e "执行结果：${GREEN}修复成功！${NC}您现在可以正常运行 ${appBashName} 了。${NC}"
fi
echo -e "本窗口可以关闭啦！"
