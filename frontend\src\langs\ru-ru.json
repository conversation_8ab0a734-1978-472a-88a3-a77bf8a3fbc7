{"name": "Русский язык", "common": {"confirm": "Подтвердить", "cancel": "Отменить", "success": "Успех", "warning": "Предупреждение", "error": "Ошибка", "save": "Сохранить", "update": "Обновить", "none": "Нет", "second": "Секунда(ы)", "minute": "Минута(ы)", "hour": "Час(ы)", "day": "День(и)", "unit_day": "д", "unit_hour": "ч", "unit_minute": "м", "unit_second": "с", "all": "Все", "key": "<PERSON><PERSON><PERSON><PERSON>", "value": "Значение", "field": "Поле", "score": "Счёт", "index": "Позиция"}, "preferences": {"name": "Настройки", "restore_defaults": "Восстановить настройки по умолчанию", "font_tip": "Поддерживается множественный выбор. Если установленный шрифт не указан в списке, введите его вручную.", "general": {"name": "Общие", "theme": "Тема", "theme_light": "Светлая", "theme_dark": "Тёмная", "theme_auto": "Авто", "language": "Язык", "system_lang": "Использовать язык системы", "font": "<PERSON>ри<PERSON><PERSON>", "font_tip": "Выберите или введите название шрифта", "font_size": "Размер шрифта", "scan_size": "Размер по умолчанию для SCAN", "scan_size_tip": "Количество элементов, возвращаемых за один раз командами SCAN/HSCAN/SSCAN/ZSCAN", "key_icon_style": "Стиль значка ключа", "key_icon_style0": "Компактный", "key_icon_style1": "Полное название", "key_icon_style2": "Точка", "key_icon_style3": "Обычный", "update": "Обновить", "auto_check_update": "Автоматически проверять обновления", "privacy": "Конфиденциальность", "allow_track": "Разрешить сбор анонимных данных"}, "editor": {"name": "Редактор", "show_linenum": "Показывать номера строк", "show_folding": "Включить сворачивание кода", "drop_text": "Разрешить перетаскивание текста", "links": "Поддержка ссылок"}, "cli": {"name": "Командная строка", "cursor_style": "Стиль курсора", "cursor_style_block": "Блок", "cursor_style_underline": "Подчёркнутый", "cursor_style_bar": "Линия"}, "decoder": {"name": "Пользовательский декодер", "new": "Новый декодер", "decoder_name": "Название", "cmd_preview": "Предпросмотр", "status": "Статус", "auto_enabled": "Автодекодирование включено", "help": "Помощь"}}, "interface": {"new_conn": "Добавить соединение", "new_group": "Добавить группу", "disconnect_all": "Отключить все", "status": "Статус", "filter": "Фильтр", "sort_conn": "Сортировать соединения", "new_conn_title": "Новое соединение", "open_db": "Открыть базу данных", "close_db": "Закрыть базу данных", "filter_key": "Фильтр ключей", "disconnect": "Отключить", "dup_conn": "Дублировать соединение", "remove_conn": "Удалить соединение", "edit_conn": "Редактировать соединение", "edit_conn_group": "Редактировать группу", "rename_conn_group": "Переименовать группу", "remove_conn_group": "Удалить группу", "import_conn": "Импортировать соединения...", "export_conn": "Экспортировать соединения...", "ttl": "TTL", "forever": "Навсегда", "rename_key": "Переименовать ключ", "delete_key": "Удалить ключ", "batch_delete_key": "Пакетное удаление ключей", "import_key": "Импортировать ключи", "flush_db": "Очистить базу данных", "check_mode": "Режим выбора", "quit_check_mode": "Выйти из режима выбора", "delete_checked": "Удалить выбранные", "export_checked": "Экспортировать выбранные", "ttl_checked": "Обновить TTL для выбранных", "copy_value": "Копировать значение", "edit_value": "Редактировать значение", "save_update": "Сохранить изменения", "score_filter_tip": "Поддерживаются операторы: \n= равно\n!= не равно\n> больше\n>= больше или равно\n< меньше\n<= меньше или равно\nНапример, >3 для значений больше 3", "add_row": "Вставить строку", "edit_row": "Редактировать строку", "delete_row": "Удалить строку", "fullscreen": "Полноэкранный режим", "offscreen": "Выйти из полноэкранного режима", "pin_edit": "Закрепить (не закрывать после сохранения)", "unpin_edit": "Открепить", "search": "Поиск", "full_search": "Полнотекстовый поиск", "full_search_result": "Содержимое соответствует '{pattern}'", "filter_field": "Фильтр полей", "filter_value": "Фильтр значений", "length": "Длина", "entries": "Записи", "memory_usage": "Использование памяти", "text_align_left": "Выравнивание по левому краю", "text_align_center": "Выравнивание по центру", "view_as": "Вид", "decode_with": "Декодировать/Распаковать", "custom_decoder": "Новый пользовательский декодер", "reload": "Перезагрузить", "reload_disable": "Перезагрузить после полной загрузки", "auto_refresh": "Автообновление", "refresh_interval": "Интервал обновления", "open_connection": "Открыть соединение", "copy_path": "Копировать путь", "copy_key": "Копировать ключ", "save_value_succ": "Значение сохранено!", "copy_succ": "Скопировано в буфер обмена!", "binary_key": "Двоичное имя ключа", "remove_key": "Удалить ключ", "new_key": "Новый ключ", "load_more": "Загрузить больше ключей", "load_all": "Загрузить все оставшиеся ключи", "load_more_entries": "Загрузить больше", "load_all_entries": "Загрузить все", "more_action": "Больше действий", "nonexist_tab_content": "Выбран<PERSON><PERSON>й ключ не существует или не выбран. Попробуйте обновить.", "empty_server_content": "Выберите и откройте соединение с левой панели", "empty_server_list": "Нет добавленных серверов Redis", "action": "Действие", "type": "Тип", "cli_welcome": "Добро пожаловать в консоль Redis Tiny RDM", "retrieving_version": "Проверка обновлений", "sub_tab": {"status": "Статус", "key_detail": "Детали ключа", "cli": "Консоль", "slow_log": "Медленный лог", "cmd_monitor": "Монитор<PERSON><PERSON><PERSON> команд", "pub_message": "Публикация/Подписка"}}, "ribbon": {"server": "Сервер", "browser": "Браузер данных", "log": "Лог", "wechat_official": "Официальный аккаунт WeChat", "follow_x": "Подписаться на 𝕏", "github": "<PERSON><PERSON><PERSON>"}, "dialogue": {"close_confirm": "Закрыть это соединение ({name})?", "edit_close_confirm": "Перед редактированием закройте соответствующие соединения. Продолжить?", "opening_connection": "Открытие соединения...", "interrupt_connection": "Отменить", "remove_tip": "{type} \"{name}\" будет удален(а/о)", "remove_group_tip": "Группа \"{name}\" и все её соединения будут удалены", "rename_binary_key_fail": "Переименование двоичного ключа не поддерживается", "handle_succ": "Успешно!", "handle_cancel": "Операция отменена.", "reload_succ": "Перезагружено!", "field_required": "Это поле обязательно для заполнения", "spec_field_required": "\"{key}\" требуется", "illegal_characters": "Содержит недопустимые символы", "connection": {"new_title": "Новое соединение", "edit_title": "Редактировать соединение", "general": "Общие", "no_group": "Без группы", "group": "Группа", "conn_name": "Название", "addr": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "usr": "Имя пользователя", "pwd": "Пароль", "name_tip": "Название соединения", "addr_tip": "Адрес сервера Redis", "sock_tip": "Unix-сокет файл Redis", "usr_tip": "(Опционально) Имя пользователя для авторизации", "pwd_tip": "(Опционально) Пароль для авторизации (Redis > 6.0)", "test": "Проверить соединение", "test_succ": "Успешно подключено к серверу Redis", "test_fail": "Не удалось подключиться", "parse_url_clipboard": "Распарсить URL из буфера обмена", "parse_pass": "Redis URL распарсен: {url}", "parse_fail": "Не удалось распарсить Redis URL: {reason}", "advn": {"title": "Дополнительно", "filter": "Фильтр ключей по умолчанию", "filter_tip": "Шаблон для фильтрации загруженных ключей", "separator": "Разделитель ключей", "separator_tip": "Разделитель сегментов пути ключа", "conn_timeout": "Тайм-аут соединения", "exec_timeout": "Тайм-аут выполнения", "dbfilter_type": "Фильтр баз данных", "dbfilter_all": "Показать все", "dbfilter_show": "Показать выбранные", "dbfilter_hide": "Скрыть выбранные", "dbfilter_show_title": "Базы данных для показа", "dbfilter_hide_title": "Базы данных для скрытия", "dbfilter_input": "Введите индекс базы данных", "dbfilter_input_tip": "Нажмите Enter для подтверждения", "key_view": "Вид ключей по умолчанию", "key_view_tree": "Древовидный", "key_view_list": "Списком", "load_size": "Ключей за загрузку", "mark_color": "Цвет маркера"}, "alias": {"title": "Псевдонимы баз данных", "db": "Введите индекс базы данных", "value": "Введите псевдоним"}, "ssl": {"title": "SSL/TLS", "enable": "Включить SSL/TLS", "allow_insecure": "Разрешить небезопасные соединения", "sni": "Имя сервера (SNI)", "sni_tip": "(Опционально) Имя сервера", "cert_file": "Файл открытого ключа", "key_file": "Файл закрытого ключа", "ca_file": "Файл CA", "cert_file_tip": "Файл открытого ключа в формате PEM (Cert)", "key_file_tip": "Файл закрытого ключа в формате PEM (Key)", "ca_file_tip": "Файл авторитета сертификации в формате PEM (CA)"}, "ssh": {"enable": "Включить SSH-туннель", "title": "SSH-туннель", "login_type": "Тип входа", "pkfile": "Файл закрытого ключа", "passphrase": "Парольная фраза", "addr_tip": "Адрес SSH-сервера", "usr_tip": "Имя пользователя SSH", "pwd_tip": "Пароль SSH", "pkfile_tip": "Путь к файлу закрытого ключа SSH", "passphrase_tip": "(Опционально) Парольная фраза для закрытого ключа"}, "sentinel": {"title": "Сентинель", "enable": "В качестве узла Сентинеля", "master": "Имя группы мастера", "auto_discover": "Автоопределение", "password": "Пароль мастера", "username": "Имя пользователя мастера", "pwd_tip": "(Опционально) Пароль мастера для авторизации (Redis > 6.0)", "usr_tip": "(Опционально) Имя пользователя мастера для авторизации"}, "cluster": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "enable": "В качестве узла кластера"}, "proxy": {"title": "Прокси", "type_none": "Без прокси", "type_system": "Прокси системы", "type_custom": "Ручная настройка прокси", "host": "Имя хоста", "auth": "Авторизация прокси", "usr_tip": "Имя пользователя для авторизации прокси", "pwd_tip": "Пароль для авторизации прокси"}}, "group": {"name": "Имя группы", "rename": "Переименовать группу", "new": "Новая группа"}, "key": {"new": "Новый ключ", "new_name": "Новое имя ключа", "server": "Соединение", "db_index": "Индекс базы данных", "key_expression": "<PERSON>а<PERSON><PERSON><PERSON><PERSON> ключей", "affected_key": "Затронутые ключи", "show_affected_key": "Показать затронутые ключи", "confirm_delete_key": "Подтвердить удаление {num} ключ(ей/ей)", "direct_delete": "Удалить совпадающий шаблон напрямую", "confirm_delete": "Подтвердить удаление", "async_delete": "Асинхронное выполнение", "async_delete_title": "Не ждать результата", "confirm_flush": "Я знаю, что делаю!", "confirm_flush_db": "Подтвердить очистку базы данных"}, "delete": {"success": "\"{key}\" удален(а/о)", "deleting": "Удаление", "doing": "Удаление ключа ({index}/{count})", "completed": "Удаление завершено, {success} успешно, {fail} с ошибкой"}, "field": {"new": "Новое поле", "new_item": "Новый элемент", "conflict_handle": "При конфликте полей", "overwrite_field": "Перезаписать", "ignore_field": "Пропустить", "insert_type": "Тип вставки", "append_item": "Добавить в конец", "prepend_item": "Добавить в начало", "enter_key": "Введите ключ", "enter_value": "Введите значение", "enter_field": "Введите имя поля", "enter_elem": "Введите элемент", "enter_member": "Введите элемент", "enter_score": "Введите счёт", "element": "Элемент", "reload_when_succ": "Перезагрузить сразу после успеха"}, "filter": {"set_key_filter": "Установить фильтр ключей", "filter_pattern": "Шабл<PERSON>н", "filter_pattern_tip": "Отфильтруйте текущий список, введя напрямую, и выполните сканирование сервера, нажав 'Enter'.\n\n* соответствует 0 или более символов, напр. 'key*'\n? соответствует одному символу, напр. 'key?'\n[] соответствует диапазону, напр. 'key[1-3]'\n\\ экранирует спецсимволы", "exact_match_tip": "Точное совпадение", "filter_type_not_support": "Фильтрация по типу не поддерживается для Redis версии 5.x и ниже"}, "export": {"name": "Экспорт данных", "export_expire_title": "Срок истечения", "export_expire": "Включить срок истечения", "export": "Экспорт", "save_file": "Путь для экспорта", "save_file_tip": "Выберите путь для сохранения экспортируемого файла", "exporting": "Экспорт ключей ({index}/{count})", "export_completed": "Экспорт заве<PERSON><PERSON><PERSON><PERSON>, {success} успешно, {fail} с ошибкой"}, "import": {"name": "Импорт данных", "import_expire_title": "Срок истечения", "reload": "Перезагрузить после импорта", "import": "Импорт", "open_csv_file": "Импортировать файл", "open_csv_file_tip": "Выберите файл для импорта", "conflict_handle": "При конфликте ключей", "conflict_overwrite": "Перезаписать", "conflict_ignore": "Пропустить", "ttl_include": "Импортировать из файла", "ttl_ignore": "Не устанавливать", "ttl_custom": "Пользовательское", "importing": "Импорт ключей импортировано/перезаписано:{imported} конфликтов/ошибок:{conflict}", "import_completed": "Импорт заве<PERSON><PERSON>ен, {success} успешно, {ignored} пропущено"}, "ttl": {"title": "Обновить TTL", "title_batch": "Пакетное обновление TTL ({count})", "quick_set": "Быстрая установка", "success": "TTL обновлен для всех ключей"}, "decoder": {"name": "Новый декодер/энкодер", "edit_name": "Редактировать декодер/энкодер", "new": "Новый", "decoder": "Декодер", "encoder": "Энкодер", "decoder_name": "Название", "auto": "Автодекодирование", "decode_path": "Путь декодера", "encode_path": "Путь энкодера", "path_help": "Путь к исполняемому файлу или алиасу cli, например 'sh/php/python'", "args": "Аргументы", "args_help": "Используйте [VALUE] в качестве заменителя для кодирования/декодирования. Если заменитель не указан, содержимое будет добавлено в конец."}, "upgrade": {"title": "Доступна новая версия", "new_version_tip": "Доступна новая версия {ver}, загрузить сейчас?", "no_update": "У вас установлена последняя версия", "download_now": "Загрузить сейчас", "later": "Позже", "skip": "Пропустить эту версию"}, "welcome": {"title": "Добро пожаловать в Tiny RDM！", "content": "Для предоставления лучшего пользовательского опыта Tiny RDM собирает некоторые анонимные данные, чтобы помочь оптимизировать программное обеспечение и улучшить пользовательский опыт. Не беспокойтесь, это не будет затрагивать вашу личную конфиденциальную информацию.\n\nЕсли у вас есть какие-либо опасения, вы можете в любое время отключить сбор данных, перейдя в «Настройки». Если у вас есть какие-либо вопросы, обращайтесь к разработчику. Надеюсь, Tiny RDM станет вашим полезным помощником!", "accept": "Помочь улучшить", "reject": "Отклонить"}, "about": {"source": "Исходный код", "website": "Официал<PERSON>ный сайт"}}, "menu": {"minimise": "Свернуть", "maximise": "Развернуть", "restore": "Восстановить", "close": "Закрыть", "preferences": "Настройки", "help": "Помощь", "user_guide": "Руководство пользователя", "check_update": "Проверить обновления...", "report_bug": "Сообщить об ошибке", "about": "О программе"}, "log": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON> запуска", "filter_server": "Фильтр сервера", "filter_keyword": "Фильтр ключевых слов", "clean_log": "Очис<PERSON><PERSON><PERSON><PERSON> журнал", "confirm_clean_log": "Подтвердите очистку журнала запуска", "exec_time": "Время выполнения", "server": "Сервер", "cmd": "Команда", "cost_time": "Затраченное время", "refresh": "Обновить"}, "status": {"uptime": "Uptime", "connected_clients": "Клиенты", "total_keys": "Клю<PERSON>и", "memory_used": "Память", "server_info": "Информация о сервере", "activity_status": "Активность", "act_cmd": "Команд/сек", "act_network_input": "Входящий трафик", "act_network_output": "Исходящий трафик", "client": {"title": "Список клиентов", "addr": "Адрес клиента", "age": "Время (сек)", "idle": "Простой (сек)", "db": "База данных"}}, "slog": {"title": "Мед<PERSON><PERSON><PERSON><PERSON>ый журнал", "limit": "<PERSON>и<PERSON><PERSON><PERSON>", "filter": "Фильтр", "exec_time": "Время", "client": "Кли<PERSON><PERSON>т", "cmd": "Команда", "cost_time": "Затраченное время"}, "monitor": {"title": "Монитор<PERSON><PERSON><PERSON> команд", "actions": "Действия", "warning": "Мониторинг команд может вызвать блокировку сервера, используйте с осторожностью на производственных серверах.", "start": "Старт", "stop": "Стоп", "search": "Поиск", "copy_log": "Копирова<PERSON><PERSON> журнал", "save_log": "Сохра<PERSON><PERSON><PERSON><PERSON> журнал", "clean_log": "Очис<PERSON><PERSON><PERSON><PERSON> журнал", "always_show_last": "Автоматическая прокрутка к последнему"}, "pubsub": {"title": "Публикация/Подписка", "publish": "Опубликовать", "subscribe": "Подписаться", "unsubscribe": "Отписаться", "clear": "Очистить сообщения", "time": "Время", "filter": "Фильтр", "channel": "<PERSON><PERSON><PERSON><PERSON>", "message": "Сообщение", "receive_message": "Получено сообщений: {total}", "always_show_last": "Автоматическая прокрутка к последнему"}}