{"name": "English", "common": {"confirm": "Confirm", "cancel": "Cancel", "success": "Success", "warning": "Warning", "error": "Error", "save": "Save", "update": "Update", "none": "None", "second": "Second(s)", "minute": "Minute(s)", "hour": "Hour(s)", "day": "Day(s)", "unit_day": "d", "unit_hour": "h", "unit_minute": "m", "unit_second": "s", "all": "All", "key": "Key", "value": "Value", "field": "Field", "score": "Score", "index": "Position"}, "preferences": {"name": "Preferences", "restore_defaults": "<PERSON><PERSON>", "font_tip": "Supports multi-selection. Manually input the font if it's not listed.", "general": {"name": "General", "theme": "Theme", "theme_light": "Light", "theme_dark": "Dark", "theme_auto": "Auto", "language": "Language", "system_lang": "Use System Language", "font": "Font", "font_tip": "Select or input font name", "font_size": "Font Size", "scan_size": "<PERSON><PERSON><PERSON> for SCAN", "scan_size_tip": "Default return number of elements for SCAN/HSCAN/SSCAN/ZSCAN", "key_icon_style": "Key Icon Style", "key_icon_style0": "Compact", "key_icon_style1": "Full Name", "key_icon_style2": "Dot", "key_icon_style3": "Common", "update": "Update", "auto_check_update": "Auto check for updates", "privacy": "Privacy", "allow_track": "Allows anonymous data to be collected"}, "editor": {"name": "Editor", "show_linenum": "Show Line Numbers", "show_folding": "Enable Code Folding", "drop_text": "Allow Drag & Drop Text", "links": "Support Links"}, "cli": {"name": "Command Line", "cursor_style": "Cursor Style", "cursor_style_block": "Block", "cursor_style_underline": "Underline", "cursor_style_bar": "Bar"}, "decoder": {"name": "Custom Decoder", "new": "New Decoder", "decoder_name": "Name", "cmd_preview": "Preview", "status": "Status", "auto_enabled": "Auto Decoding Enabled", "help": "Help"}}, "interface": {"new_conn": "Add Connection", "new_group": "Add Group", "disconnect_all": "Disconnect All", "status": "Status", "filter": "Filter", "sort_conn": "Sort Connections", "new_conn_title": "New Connection", "open_db": "Open Database", "close_db": "Close Database", "filter_key": "<PERSON><PERSON>", "disconnect": "Disconnect", "dup_conn": "Duplicate Connection", "remove_conn": "Remove Connection", "edit_conn": "Edit Connection", "edit_conn_group": "Edit Group", "rename_conn_group": "Rename Group", "remove_conn_group": "Remove Group", "import_conn": "Import Connections...", "export_conn": "Export Connections...", "ttl": "TTL", "forever": "Forever", "rename_key": "<PERSON><PERSON>", "delete_key": "Delete Key", "batch_delete_key": "<PERSON>ch Delete Keys", "import_key": "Import Keys", "flush_db": "Flush Database", "check_mode": "Check Mode", "quit_check_mode": "Exit Check Mode", "delete_checked": "Delete Checked", "export_checked": "Export Checked", "ttl_checked": "Update TTL for Checked", "copy_value": "Copy Value", "edit_value": "Edit Value", "save_update": "Save Changes", "score_filter_tip": "Support operators:\n= equal\n!= not equal\n> greater than\n>= greater than or equal\n< less than\n<= less than or equal\ne.g. >3 for scores greater than 3", "add_row": "Insert Row", "edit_row": "Edit Row", "delete_row": "Delete Row", "fullscreen": "Full Screen", "offscreen": "Exit Full Screen", "pin_edit": "<PERSON><PERSON> (Stay open after save)", "unpin_edit": "Unpin", "search": "Search", "full_search": "Full Text Search", "full_search_result": "Content matched '{pattern}'", "filter_field": "<PERSON><PERSON>", "filter_value": "Filter Value", "length": "Length", "entries": "Entries", "memory_usage": "Memory Usage", "text_align_left": "Text Align Left", "text_align_center": "Text Align Center", "view_as": "View As", "decode_with": "Decode / Decompress", "custom_decoder": "New Custom Decoder", "reload": "Reload", "reload_disable": "Reload after fully loaded", "auto_refresh": "Auto Refresh", "refresh_interval": "Refresh Interval", "open_connection": "Open Connection", "copy_path": "Copy Path", "copy_key": "Copy Key", "save_value_succ": "Value Saved!", "copy_succ": "Copied to Clipboard!", "binary_key": "Binary Key Name", "remove_key": "Remove Key", "new_key": "New Key", "load_more": "Load <PERSON>", "load_all": "Load Remaining Keys", "load_more_entries": "Load More", "load_all_entries": "Load All", "more_action": "More Actions", "nonexist_tab_content": "Selected key does not exist or none selected. Retry after refresh.", "empty_server_content": "Select and open a connection from the left panel", "empty_server_list": "No Redis server added", "action": "Action", "type": "Type", "cli_welcome": "Welcome to Tiny RDM Redis Console", "retrieving_version": "Checking for updates", "sub_tab": {"status": "Status", "key_detail": "Key Detail", "cli": "<PERSON><PERSON><PERSON>", "slow_log": "Slow Log", "cmd_monitor": "Monitor Commands", "pub_message": "Pub/Sub"}}, "ribbon": {"server": "Server", "browser": "Data Browser", "log": "Log", "wechat_official": "WeChat Official Account", "follow_x": "Follow 𝕏", "github": "<PERSON><PERSON><PERSON>"}, "dialogue": {"close_confirm": "Close this connection ({name})?", "edit_close_confirm": "Please close relevant connections before editing. Continue?", "opening_connection": "Opening Connection...", "interrupt_connection": "Cancel", "remove_tip": "{type} \"{name}\" will be deleted", "remove_group_tip": "Group \"{name}\" and all its connections will be deleted", "rename_binary_key_fail": "Renaming binary key is not supported", "handle_succ": "Success!", "handle_cancel": "Operation canceled.", "reload_succ": "Reloaded!", "field_required": "This field is required", "spec_field_required": "\"{key}\" is required", "illegal_characters": "Contains illegal characters", "connection": {"new_title": "New Connection", "edit_title": "Edit Connection", "general": "General", "no_group": "No Group", "group": "Group", "conn_name": "Name", "addr": "Address", "usr": "Username", "pwd": "Password", "name_tip": "Connection name", "addr_tip": "Redis server address", "sock_tip": "Redis unix socket file", "usr_tip": "(Optional) Auth username", "pwd_tip": "(Optional) Auth password (Redis > 6.0)", "test": "Test Connection", "test_succ": "Successfully connected to Redis server", "test_fail": "Connection failed", "parse_url_clipboard": "Parse URL from Clipboard", "parse_pass": "Redis URL parsed: {url}", "parse_fail": "Failed to parse Redis URL: {reason}", "advn": {"title": "Advanced", "filter": "Default Key Filter", "filter_tip": "Pattern to filter loaded keys", "separator": "Key Separator", "separator_tip": "Separator for key path segments", "conn_timeout": "Connection Timeout", "exec_timeout": "Execution Timeout", "dbfilter_type": "Database Filter", "dbfilter_all": "Show All", "dbfilter_show": "Show Selected", "dbfilter_hide": "<PERSON>de Selected", "dbfilter_show_title": "Databases to Show", "dbfilter_hide_title": "Databases to Hide", "dbfilter_input": "Input Database Index", "dbfilter_input_tip": "Press Enter to confirm", "key_view": "Default Key View", "key_view_tree": "Tree View", "key_view_list": "List View", "load_size": "<PERSON>", "mark_color": "Mark <PERSON>"}, "alias": {"title": "Database Alias", "db": "Input Database Index", "value": "Input Database Alias"}, "ssl": {"title": "SSL/TLS", "enable": "Enable SSL/TLS", "allow_insecure": "Allow Insecure", "sni": "Server Name (SNI)", "sni_tip": "(Optional) Server name", "cert_file": "Public Key File", "key_file": "Private Key File", "ca_file": "CA File", "cert_file_tip": "Public Key File in PEM format(Cert)", "key_file_tip": "Private Key File in PEM format(Key)", "ca_file_tip": "Certificate Authority File in PEM format(CA)"}, "ssh": {"enable": "Enable SSH Tunnel", "title": "SSH Tunnel", "login_type": "Login Type", "pkfile": "Private Key File", "passphrase": "Passphrase", "addr_tip": "SSH Server Address", "usr_tip": "SSH Username", "pwd_tip": "SSH Password", "pkfile_tip": "SSH private key file path", "passphrase_tip": "(Optional) Passphrase for private key"}, "sentinel": {"title": "Sentinel", "enable": "As Sentinel Node", "master": "Master Group Name", "auto_discover": "Auto Discover", "password": "Master Password", "username": "Master <PERSON><PERSON><PERSON>", "pwd_tip": "(Optional) Master auth password (Redis > 6.0)", "usr_tip": "(Optional) Master auth username"}, "cluster": {"title": "Cluster", "enable": "As Cluster Node"}, "proxy": {"title": "Proxy", "type_none": "No Proxy", "type_system": "System Proxy", "type_custom": "Manual Proxy", "host": "Hostname", "auth": "Proxy Authentication", "usr_tip": "Proxy auth username", "pwd_tip": "Proxy auth password"}}, "group": {"name": "Group Name", "rename": "Rename Group", "new": "New Group"}, "key": {"new": "New Key", "new_name": "New Key Name", "server": "Connection", "db_index": "Database Index", "key_expression": "Key Pattern", "affected_key": "Affected Keys", "show_affected_key": "Show Affected Keys", "confirm_delete_key": "Confirm delete {num} key(s)", "direct_delete": "Delete match pattern directly", "confirm_delete": "Confirm Delete", "async_delete": "Async Execution", "async_delete_title": "Don't wait for result", "confirm_flush": "I know what I'm doing!", "confirm_flush_db": "Confirm flush database"}, "delete": {"success": "\"{key}\" deleted", "deleting": "Deleting", "doing": "Deleting key ({index}/{count})", "completed": "Deletion completed, {success} succeeded, {fail} failed"}, "field": {"new": "New Field", "new_item": "New Item", "conflict_handle": "On Field Conflict", "overwrite_field": "Overwrite", "ignore_field": "Ignore", "insert_type": "Insert Type", "append_item": "Append", "prepend_item": "Prepend", "enter_key": "Enter Key", "enter_value": "Enter Value", "enter_field": "Enter Field Name", "enter_elem": "Enter Element", "enter_member": "Enter Member", "enter_score": "Enter Score", "element": "Element", "reload_when_succ": "Reload immediately if success"}, "filter": {"set_key_filter": "Set Key Filter", "filter_pattern": "Pattern", "filter_pattern_tip": "Filter by directly input, and scan by press 'Enter'.\n\n* matches 0 or more chars, e.g. 'key*'  \n? matches single char, e.g. 'key?'\n[] matches range, e.g. 'key[1-3]'\n\\ escapes special chars", "exact_match_tip": "Exact Match", "filter_type_not_support": "Type filtering is not supported for Redis 5.x and below."}, "export": {"name": "Export Data", "export_expire_title": "Expiration", "export_expire": "Include Expiration", "export": "Export", "save_file": "Export Path", "save_file_tip": "Select path to save exported file", "exporting": "Exporting keys ({index}/{count})", "export_completed": "Export completed, {success} succeeded, {fail} failed"}, "import": {"name": "Import Data", "import_expire_title": "Expiration", "import": "Import", "reload": "Reload After Import", "open_csv_file": "Import File", "open_csv_file_tip": "Select file to import", "conflict_handle": "On Key Conflict", "conflict_overwrite": "Overwrite", "conflict_ignore": "Ignore", "ttl_include": "Import From File", "ttl_ignore": "Do Not Set", "ttl_custom": "Custom", "importing": "Importing keys imported/overwritten:{imported} conflict/failed:{conflict}", "import_completed": "Import completed, {success} succeeded, {ignored} ignored"}, "ttl": {"title": "Update TTL", "title_batch": "Batch Update TTL ({count})", "quick_set": "Quick Set", "success": "TTL updated for all keys"}, "decoder": {"name": "New Decoder/Encoder", "edit_name": "Edit Decoder/Encoder", "new": "New", "decoder": "Decoder", "encoder": "Encoder", "decoder_name": "Name", "auto": "Auto Decode", "decode_path": "Decoder Path", "encode_path": "Encoder Path", "path_help": "Path to executable, or cli alias like 'sh/php/python'", "args": "Arguments", "args_help": "Use [VALUE] as placeholder for encoding/decoding content. The content will be appended to the end if no placeholder is provided."}, "upgrade": {"title": "New Version Available", "new_version_tip": "New version {ver} available, download now?", "no_update": "You're up-to-date", "download_now": "Download Now", "later": "Later", "skip": "Skip This Version"}, "welcome": {"title": "Welcome to Tiny RDM！", "content": "In order to provide a better user experience, Tiny RDM collects some anonymous data to help optimize the software and improve the user experience, please rest assured that this does not involve your personal privacy information.\n\nIf you have any concerns, you can turn off this data collection feature at any time by going to Preferences. If you have any questions, feel free to contact the developer. I hope Tiny RDM can become your helpful assistant!", "accept": "Help Improve", "reject": "Reject"}, "about": {"source": "Source Code", "website": "Official Website"}}, "menu": {"minimise": "Minimise", "maximise": "<PERSON><PERSON>", "restore": "Rest<PERSON>", "close": "Close", "preferences": "Preferences", "help": "Help", "user_guide": "User Guide", "check_update": "Check for Updates...", "report_bug": "Report Bug", "about": "About"}, "log": {"title": "Launch Log", "filter_server": "Filter Server", "filter_keyword": "Filter Keyword", "clean_log": "Clean Log", "confirm_clean_log": "Confirm clean launch log", "exec_time": "Exec Time", "server": "Server", "cmd": "Command", "cost_time": "Cost", "refresh": "Refresh"}, "status": {"uptime": "Uptime", "connected_clients": "Clients", "total_keys": "Keys", "memory_used": "Memory", "server_info": "Server Info", "activity_status": "Activity", "act_cmd": "Commands/Sec", "act_network_input": "Network Input", "act_network_output": "Network Output", "client": {"title": "Client List", "addr": "Client Address", "age": "Age (sec)", "idle": "Idle (sec)", "db": "Database"}}, "slog": {"title": "Slow Log", "limit": "Limit", "filter": "Filter", "exec_time": "Time", "client": "Client", "cmd": "Command", "cost_time": "Cost"}, "monitor": {"title": "Monitor Commands", "actions": "Actions", "warning": "Command monitoring may cause server blocking, use with caution on production servers.", "start": "Start", "stop": "Stop", "search": "Search", "copy_log": "Copy Log", "save_log": "Save Log", "clean_log": "Clean Log", "always_show_last": "Auto Scroll to Latest"}, "pubsub": {"title": "Pub/Sub", "publish": "Publish", "subscribe": "Subscribe", "unsubscribe": "Unsubscribe", "clear": "Clear Messages", "time": "Time", "filter": "Filter", "channel": "Channel", "message": "Message", "receive_message": "Received {total} messages", "always_show_last": "Auto Scroll to Latest"}}