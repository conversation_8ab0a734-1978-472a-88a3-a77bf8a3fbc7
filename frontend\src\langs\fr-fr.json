{"name": "Français", "common": {"confirm": "Confirmer", "cancel": "Annuler", "success": "Su<PERSON>ès", "warning": "Avertissement", "error": "<PERSON><PERSON><PERSON>", "save": "Enregistrer", "update": "Mettre à jour", "none": "Aucun", "second": "Seconde(s)", "minute": "Minute(s)", "hour": "Heure(s)", "day": "Jour(s)", "unit_day": "j", "unit_hour": "h", "unit_minute": "m", "unit_second": "s", "all": "Tous", "key": "Clé", "value": "<PERSON><PERSON>", "field": "<PERSON><PERSON>", "score": "Score", "index": "Position"}, "preferences": {"name": "Préférences", "restore_defaults": "Restaurer les valeurs par défaut", "font_tip": "Supporte la sélection multiple. Saisir manuellement la police si elle n'est pas listée.", "general": {"name": "Général", "theme": "Thème", "theme_light": "<PERSON>", "theme_dark": "Sombre", "theme_auto": "Automatique", "language": "<PERSON><PERSON>", "system_lang": "Utiliser la langue du système", "font": "Police", "font_tip": "<PERSON><PERSON><PERSON><PERSON><PERSON> ou saisir le nom de la police", "font_size": "Taille de la police", "scan_size": "Taille par défaut pour SCAN", "scan_size_tip": "Nombre d'éléments retournés par les commandes SCAN/HSCAN/SSCAN/ZSCAN", "key_icon_style": "Style d'icône de clé", "key_icon_style0": "Compact", "key_icon_style1": "Nom complet", "key_icon_style2": "Point", "key_icon_style3": "<PERSON><PERSON><PERSON>", "update": "Mise à jour", "auto_check_update": "Vérifier automatiquement les mises à jour", "privacy": "Politique de confidentialité", "allow_track": "Autoriser la collecte de données anonymes"}, "editor": {"name": "<PERSON><PERSON><PERSON>", "show_linenum": "Afficher les numéros de ligne", "show_folding": "Activer le repliage de code", "drop_text": "Autoriser le glisser-déposer de texte", "links": "Supporter les liens"}, "cli": {"name": "Ligne de commande", "cursor_style": "Style du curseur", "cursor_style_block": "Bloc", "cursor_style_underline": "Soulignement", "cursor_style_bar": "<PERSON><PERSON>"}, "decoder": {"name": "<PERSON><PERSON><PERSON><PERSON>", "new": "Nouveau décodeur", "decoder_name": "Nom", "cmd_preview": "<PERSON><PERSON><PERSON><PERSON>", "status": "Statut", "auto_enabled": "Décodage automatique activé", "help": "Aide"}}, "interface": {"new_conn": "Ajouter une connexion", "new_group": "Ajouter un groupe", "disconnect_all": "Déconnecter tout", "status": "Statut", "filter": "Filtre", "sort_conn": "Trier les connexions", "new_conn_title": "Nouvelle connexion", "open_db": "<PERSON>u<PERSON><PERSON>r la base de données", "close_db": "Fermer la base de donn<PERSON>", "filter_key": "Filtrer les clés", "disconnect": "Déconnecter", "dup_conn": "Dupliquer la connexion", "remove_conn": "Supprimer la connexion", "edit_conn": "Éditer la connexion", "edit_conn_group": "Éditer le groupe", "rename_conn_group": "Renommer le groupe", "remove_conn_group": "Supprimer le groupe", "import_conn": "Importer des connexions...", "export_conn": "Exporter des connexions...", "ttl": "TTL", "forever": "Pour toujours", "rename_key": "Renommer la clé", "delete_key": "Supprimer la clé", "batch_delete_key": "Supprimer les clés par lot", "import_key": "Importer des clés", "flush_db": "Vider la base de données", "check_mode": "Mode de vérification", "quit_check_mode": "<PERSON><PERSON><PERSON> le mode de vérification", "delete_checked": "Supprimer les éléments cochés", "export_checked": "Exporter les éléments cochés", "ttl_checked": "Mettre à jour le TTL des éléments cochés", "copy_value": "<PERSON><PERSON><PERSON> la valeur", "edit_value": "É<PERSON>er la valeur", "save_update": "Enregistrer les modifications", "score_filter_tip": "Supporte les opérateurs :\n= égal\n!= différent\n> supérieur à \n>= supérieur ou égal\n< inférieur à\n<= inférieur ou égal\nEx: >3 pour les scores supérieurs à 3", "add_row": "Ins<PERSON>rer une ligne", "edit_row": "Éditer la ligne", "delete_row": "Supprimer la ligne", "fullscreen": "Plein écran", "offscreen": "<PERSON><PERSON><PERSON> le plein écran", "pin_edit": "<PERSON><PERSON><PERSON> (rester ouvert après enregistrement)", "unpin_edit": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "search": "<PERSON><PERSON><PERSON>", "full_search": "Recherche en texte intégral", "full_search_result": "Contenu correspondant à '{pattern}'", "filter_field": "<PERSON>lt<PERSON> le champ", "filter_value": "Valeur de filtrage", "length": "<PERSON><PERSON><PERSON>", "entries": "Entrées", "memory_usage": "Utilisation de la mémoire", "text_align_left": "<PERSON><PERSON><PERSON> à gauche", "text_align_center": "Centrer", "view_as": "Voir comme", "decode_with": "Décoder / Décompresser", "custom_decoder": "Nouveau décodeur personnalisé", "reload": "Recharger", "reload_disable": "Recharger après chargement complet", "auto_refresh": "Rafraîchissement automatique", "refresh_interval": "Intervalle de rafraîchissement", "open_connection": "Ouvrir la connexion", "copy_path": "<PERSON><PERSON><PERSON> le chemin", "copy_key": "Co<PERSON>r la clé", "save_value_succ": "Valeur enregistrée !", "copy_succ": "Copié dans le presse-papiers !", "binary_key": "Clé binaire", "remove_key": "Supprimer la clé", "new_key": "Nouvelle clé", "load_more": "Charger plus de clés", "load_all": "Charger toutes les clés restantes", "load_more_entries": "Charger plus", "load_all_entries": "Charger tout", "more_action": "Plus d'actions", "nonexist_tab_content": "La clé sélectionnée n'existe pas ou aucune clé n'est sélectionnée. Réessayez après un rafraîchissement.", "empty_server_content": "Sélectionnez et ouvrez une connexion depuis le panneau de gauche", "empty_server_list": "Aucun serveur <PERSON> a<PERSON>", "action": "Action", "type": "Type", "cli_welcome": "Bienvenue dans la console Redis de Tiny RDM", "retrieving_version": "Vérification des mises à jour", "sub_tab": {"status": "Statut", "key_detail": "Dé<PERSON> de la clé", "cli": "<PERSON><PERSON><PERSON>", "slow_log": "Journal lent", "cmd_monitor": "Surveiller les commandes", "pub_message": "Pub/Sub"}}, "ribbon": {"server": "Ser<PERSON><PERSON>", "browser": "Navigateur de données", "log": "Journal", "wechat_official": "Compte officiel WeChat", "follow_x": "Suivre 𝕏", "github": "<PERSON><PERSON><PERSON>"}, "dialogue": {"close_confirm": "Fermer cette connexion ({name}) ?", "edit_close_confirm": "Veuillez fermer les connexions appropriées avant l'édition. Continuer ?", "opening_connection": "Ouverture de la connexion...", "interrupt_connection": "Annuler", "remove_tip": "{type} \"{name}\" sera supprimé", "remove_group_tip": "Le groupe \"{name}\" et toutes ses connexions seront supprimés", "rename_binary_key_fail": "Le renommage des clés binaires n'est pas pris en charge", "handle_succ": "Succès !", "handle_cancel": "Opération annulée.", "reload_succ": "Rechargé !", "field_required": "Ce champ est obligatoire", "spec_field_required": "\"{key}\" est requis", "illegal_characters": "Contient des caractères illégaux", "connection": {"new_title": "Nouvelle connexion", "edit_title": "Éditer la connexion", "general": "Général", "no_group": "Aucun groupe", "group": "Groupe", "conn_name": "Nom", "addr": "<PERSON><PERSON><PERSON>", "usr": "Nom d'utilisateur", "pwd": "Mot de passe", "name_tip": "Nom de la connexion", "addr_tip": "<PERSON><PERSON><PERSON> <PERSON>ur <PERSON>is", "sock_tip": "Fichier de socket Unix Redis", "usr_tip": "(Optionnel) Nom d'utilisateur d'authentification", "pwd_tip": "(Optionnel) Mot de passe d'authentification (Redis > 6.0)", "test": "Tester la connexion", "test_succ": "Connecté avec succès au serveur Redis", "test_fail": "Échec de la connexion", "parse_url_clipboard": "Analyser l'URL depuis le presse-papiers", "parse_pass": "URL Redis analysée : {url}", "parse_fail": "Échec de l'analyse de l'URL Redis : {reason}", "advn": {"title": "<PERSON><PERSON><PERSON>", "filter": "Filtre de clé par défaut", "filter_tip": "Modèle pour filtrer les clés chargées", "separator": "Séparateur de clé", "separator_tip": "Séparateur pour les segments de chemin de clé", "conn_timeout": "Délai d'expiration de la connexion", "exec_timeout": "<PERSON><PERSON><PERSON> d'exé<PERSON>ion", "dbfilter_type": "Filtre de base de données", "dbfilter_all": "<PERSON><PERSON> afficher", "dbfilter_show": "Afficher la sélection", "dbfilter_hide": "Masquer la sélection", "dbfilter_show_title": "Bases de données à afficher", "dbfilter_hide_title": "Bases de données à masquer", "dbfilter_input": "Saisir l'index de la base de données", "dbfilter_input_tip": "Appuyer sur Entrée pour confirmer", "key_view": "Vue de clé par défaut", "key_view_tree": "Vue arborescente", "key_view_list": "<PERSON><PERSON> liste", "load_size": "Clés par chargement", "mark_color": "<PERSON><PERSON><PERSON> de marquage"}, "alias": {"title": "Alias de base de données", "db": "Saisir l'index de la base de données", "value": "<PERSON><PERSON> l'alias de la base de donn<PERSON>"}, "ssl": {"title": "SSL/TLS", "enable": "Activer SSL/TLS", "allow_insecure": "Autoriser les connexions non sécurisées", "sni": "Nom du serveur (SNI)", "sni_tip": "(Optionnel) Nom du serveur", "cert_file": "Fichier de clé publique", "key_file": "Fichier de clé privée", "ca_file": "Fichier CA", "cert_file_tip": "Fichier de clé publique au format PEM(Cert)", "key_file_tip": "Fichier de clé privée au format PEM(Key)", "ca_file_tip": "Fichier d'autorité de certification au format PEM(CA)"}, "ssh": {"enable": "Activer le tunnel SSH", "title": "Tunnel SSH", "login_type": "Type de connexion", "pkfile": "Fichier de clé privée", "passphrase": "Phrase secrète", "addr_tip": "Adresse SSH", "usr_tip": "Nom d'utilisateur SSH", "pwd_tip": "Mot de passe SSH", "pkfile_tip": "Chemin du fichier de clé privée SSH", "passphrase_tip": "(Optionnel) Phrase secrète pour la clé privée"}, "sentinel": {"title": "Sentinelle", "enable": "En tant que noeud sentinelle", "master": "Nom du groupe principal", "auto_discover": "Découverte automatique", "password": "Mot de passe principal", "username": "Nom d'utilisateur principal", "pwd_tip": "(Optionnel) Mot de passe d'authentification principal (Redis > 6.0)", "usr_tip": "(Optionnel) Nom d'utilisateur d'authentification principal"}, "cluster": {"title": "Cluster", "enable": "En tant que noeud de cluster"}, "proxy": {"title": "Proxy", "type_none": "Pas de proxy", "type_system": "Proxy système", "type_custom": "Proxy manuel", "host": "Nom d'hôte", "auth": "Authentification proxy", "usr_tip": "Nom d'utilisateur d'authentification proxy", "pwd_tip": "Mot de passe d'authentification proxy"}}, "group": {"name": "Nom du groupe", "rename": "Renommer le groupe", "new": "Nouveau groupe"}, "key": {"new": "Nouvelle clé", "new_name": "Nouveau nom de clé", "server": "Connexion", "db_index": "Index de la base de données", "key_expression": "<PERSON><PERSON><PERSON><PERSON>", "affected_key": "Clés affectées", "show_affected_key": "Afficher les clés affectées", "confirm_delete_key": "Confirmer la <PERSON> de {num} clé(s)", "direct_delete": "Supprimer le modèle correspondant directement", "confirm_delete": "Confirmer la <PERSON>", "async_delete": "Exécution asynchrone", "async_delete_title": "Ne pas attendre le résultat", "confirm_flush": "Je sais ce que je fais !", "confirm_flush_db": "Confirmer le vidage de la base de données"}, "delete": {"success": "\"{key}\" supprimé", "deleting": "Suppression en cours", "doing": "Suppression de la clé ({index}/{count})", "completed": "Suppression terminée, {success} ré<PERSON>ies, {fail} <PERSON><PERSON><PERSON><PERSON>"}, "field": {"new": "Nouveau champ", "new_item": "Nouvel élément", "conflict_handle": "En cas de conflit de champ", "overwrite_field": "<PERSON><PERSON><PERSON><PERSON>", "ignore_field": "<PERSON><PERSON><PERSON>", "insert_type": "Type d'insertion", "append_item": "Ajouter", "prepend_item": "Insérer en tête", "enter_key": "<PERSON><PERSON> la clé", "enter_value": "<PERSON><PERSON> la valeur", "enter_field": "<PERSON><PERSON> le nom du champ", "enter_elem": "Saisir l'élément", "enter_member": "<PERSON><PERSON> le membre", "enter_score": "<PERSON><PERSON> le score", "element": "É<PERSON>ment", "reload_when_succ": "Recharger immédiatement en cas de réussite"}, "filter": {"set_key_filter": "Définir le filtre de clé", "filter_pattern": "<PERSON><PERSON><PERSON><PERSON>", "filter_pattern_tip": "Filtrez la liste actuelle en saisissant directement, et scannez le serveur en appuyant sur 'Entrée'.\n\n* correspond à 0 ou plusieurs caractères, ex : 'key*'\n? correspond à un seul caractère, ex : 'key?'\n[] correspond à une plage, ex : 'key[1-3]' échappe les caractères spéciaux", "exact_match_tip": "Correspondance exacte", "filter_type_not_support": "Le filtrage par type n’est pas pris en charge pour Redis 5.x et les versions antérieures"}, "export": {"name": "Exporter les données", "export_expire_title": "Expiration", "export_expire": "Inclure l'expiration", "export": "Exporter", "save_file": "Chemin d'exportation", "save_file_tip": "Sélectionner le chemin pour enregistrer le fichier exporté", "exporting": "Exportation des clés ({index}/{count})", "export_completed": "Exportation terminée, {success} ré<PERSON>ies, {fail} <PERSON><PERSON><PERSON><PERSON>"}, "import": {"name": "Importer des données", "import_expire_title": "Expiration", "reload": "Recharger après l'importation", "import": "Importer", "open_csv_file": "Fichier d'importation", "open_csv_file_tip": "Sélectionner le fichier à importer", "conflict_handle": "En cas de conflit de clé", "conflict_overwrite": "<PERSON><PERSON><PERSON><PERSON>", "conflict_ignore": "<PERSON><PERSON><PERSON>", "ttl_include": "Importer depuis le fichier", "ttl_ignore": "Ne pas définir", "ttl_custom": "<PERSON><PERSON><PERSON><PERSON>", "importing": "Importation des clés importées/écrasées:{imported} conflit/échouées:{conflict}", "import_completed": "Importation terminée, {success} ré<PERSON>ies, {ignored} ignorées"}, "ttl": {"title": "Mettre à jour le TTL", "title_batch": "Mise à jour par lot du TTL ({count})", "quick_set": "Définition rapide", "success": "TTL mis à jour pour toutes les clés"}, "decoder": {"name": "Nouveau décodeur/encodeur", "edit_name": "<PERSON><PERSON><PERSON> le décodeur/encodeur", "new": "Nouveau", "decoder": "<PERSON><PERSON><PERSON><PERSON>", "encoder": "Encodeur", "decoder_name": "Nom", "auto": "Décodage automatique", "decode_path": "<PERSON><PERSON><PERSON> <PERSON>", "encode_path": "<PERSON><PERSON><PERSON> de l'encodeur", "path_help": "Chemin de l'exécutable, ou alias cli comme 'sh/php/python'", "args": "Arguments", "args_help": "Utiliser [VALUE] comme espace réservé pour le contenu de codage/décodage. Le contenu sera ajouté à la fin si aucun espace réservé n'est fourni."}, "upgrade": {"title": "Nouvelle version disponible", "new_version_tip": "Nouvelle version {ver} disponible, télécharger maintenant ?", "no_update": "Vous êtes à jour", "download_now": "Télécharger maintenant", "later": "Plus tard", "skip": "Ignorer cette version"}, "welcome": {"title": "Bienvenue dans Tiny RDM!", "content": "<PERSON><PERSON> d'offrir une meilleure expérience utilisateur, Tiny RDM collecte certaines données anonymes pour aider à optimiser le logiciel et améliorer l'expérience utilisateur. Soyez assuré que cela n'implique pas vos informations personnelles et privées.\n\nSi vous avez des inquiétudes, vous pouvez désactiver cette fonction de collecte de données à tout moment en allant dans les Préférences. Si vous avez des questions, n'hésitez pas à contacter le développeur. J'espère que Tiny RDM pourra être un bon assistant pour vous!", "accept": "<PERSON><PERSON> <PERSON>", "reject": "<PERSON><PERSON><PERSON>"}, "about": {"source": "Code source", "website": "Site officiel"}}, "menu": {"minimise": "Minimiser", "maximise": "Maximiser", "restore": "<PERSON><PERSON><PERSON>", "close": "<PERSON><PERSON><PERSON>", "preferences": "Préférences", "help": "Aide", "user_guide": "Guide de l'utilisateur", "check_update": "Vérifier les mises à jour...", "report_bug": "Signaler un bug", "about": "À propos"}, "log": {"title": "Journal de lancement", "filter_server": "<PERSON><PERSON><PERSON> le serveur", "filter_keyword": "Filtrer les mots-clés", "clean_log": "Nettoyer le journal", "confirm_clean_log": "Confirmer le nettoyage du journal de lancement", "exec_time": "Heure d'exécution", "server": "Ser<PERSON><PERSON>", "cmd": "Commande", "cost_time": "Coût", "refresh": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "status": {"uptime": "Temps de fonctionnement", "connected_clients": "Clients connectés", "total_keys": "Nombre total de clés", "memory_used": "Mémoire utilisée", "server_info": "Informations serveur", "activity_status": "Activité", "act_cmd": "Commandes/Sec", "act_network_input": "<PERSON><PERSON><PERSON>", "act_network_output": "<PERSON><PERSON><PERSON>", "client": {"title": "Liste des clients", "addr": "<PERSON><PERSON><PERSON> client", "age": "Âge (sec)", "idle": "Inactif (sec)", "db": "Base de données"}}, "slog": {"title": "Journal lent", "limit": "Limite", "filter": "Filtre", "exec_time": "<PERSON><PERSON>", "client": "Client", "cmd": "Commande", "cost_time": "Coût"}, "monitor": {"title": "Surveiller les commandes", "actions": "Actions", "warning": "La surveillance des commandes peut bloquer le serveur, à utiliser avec prudence sur les serveurs de production.", "start": "<PERSON><PERSON><PERSON><PERSON>", "stop": "<PERSON><PERSON><PERSON><PERSON>", "search": "<PERSON><PERSON><PERSON>", "copy_log": "Copier le journal", "save_log": "Enregistrer le journal", "clean_log": "Nettoyer le journal", "always_show_last": "Défilement automatique vers le dernier message"}, "pubsub": {"title": "Pub/Sub", "publish": "Publier", "subscribe": "<PERSON>'abonner", "unsubscribe": "<PERSON> d<PERSON>ab<PERSON>ner", "clear": "Effacer les messages", "time": "<PERSON><PERSON>", "filter": "Filtre", "channel": "Canal", "message": "Message", "receive_message": "{total} messages reçus", "always_show_last": "Défilement automatique vers le dernier message"}}