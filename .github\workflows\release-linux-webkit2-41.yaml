name: Release Linux App

on:
  release:
    types: [ published ]
  workflow_dispatch:
    inputs:
      tag:
        description: 'Version tag'
        required: true
        default: '1.0.0'

jobs:
  release:
    name: Release Linux App
    runs-on: ubuntu-24.04
    strategy:
      matrix:
        platform:
          - linux/amd64

    steps:
      - name: Checkout source code
        uses: actions/checkout@v3

      - name: Normalise platform tag
        id: normalise_platform
        shell: bash
        run: |
          tag=$(echo ${{ matrix.platform }} | sed -e 's/\//_/g')
          echo "tag=$tag" >> "$GITHUB_OUTPUT"

      - name: Normalise platform arch
        id: normalise_platform_arch
        run: |
           if [ "${{ matrix.platform }}" == "linux/amd64" ]; then
             echo "arch=x86_64" >> "$GITHUB_OUTPUT"
           elif [ "${{ matrix.platform }}" == "linux/aarch64" ]; then
             echo "arch=aarch64" >> "$GITHUB_OUTPUT"
           fi

      - name: Normalise version tag
        id: normalise_version
        shell: bash
        run: |
          if [ "${{ github.event.release.tag_name }}" == "" ]; then
            version=$(echo ${{ github.event.inputs.tag }} | sed -e 's/v//g')
            echo "version=$version" >> "$GITHUB_OUTPUT"
          else
            version=$(echo ${{ github.event.release.tag_name }} | sed -e 's/v//g')
            echo "version=$version" >> "$GITHUB_OUTPUT"
          fi

      - name: Setup Go
        uses: actions/setup-go@v4
        with:
          go-version: stable

      - name: Install wails
        shell: bash
        run: go install github.com/wailsapp/wails/v2/cmd/wails@latest

      - name: Install Ubuntu prerequisites
        shell: bash
        run: |
          sudo apt-get update
          sudo apt-get install -y libgtk-3-dev libwebkit2gtk-4.1-dev libfuse-dev libfuse2

      - name: Setup Node
        uses: actions/setup-node@v3
        with:
          node-version: 20

      - name: Build frontend assets
        shell: bash
        run: |
          npm install -g npm@9
          jq '.info.productVersion = "${{ steps.normalise_version.outputs.version }}"' wails.json > tmp.json
          mv tmp.json wails.json
          cd frontend
          jq '.version = "${{ steps.normalise_version.outputs.version }}"' package.json > tmp.json
          mv tmp.json package.json
          npm install

      - name: Build wails app for Linux
        shell: bash
        run: |
          CGO_ENABLED=1 wails build -platform ${{ matrix.platform }} \
          -ldflags "-X main.version=v${{ steps.normalise_version.outputs.version }} -X main.gaMeasurementID=${{ secrets.GA_MEASUREMENT_ID }} -X main.gaSecretKey=${{ secrets.LINUX_GA_SECRET }}" \
          -tags webkit2_41 \
          -o tiny-rdm

      - name: Setup control template
        shell: bash
        run: |
          content=$(cat build/linux/tiny-rdm_0.0.0_amd64/DEBIAN/control)
          name=$(jq -r '.name' wails.json | tr -d ' ' | tr '[:upper:]' '[:lower:]')
          content=$(echo "$content" | sed -e "s/{{.Name}}/$name/g")
          content=$(echo "$content" | sed -e "s/{{.Info.ProductVersion}}/$(jq -r '.info.productVersion' wails.json)/g")
          content=$(echo "$content" | sed -e "s/{{.Author.Name}}/$(jq -r '.author.name' wails.json)/g")
          content=$(echo "$content" | sed -e "s/{{.Author.Email}}/$(jq -r '.author.email' wails.json)/g")
          content=$(echo "$content" | sed -e "s/{{.Info.Comments}}/$(jq -r '.info.comments' wails.json)/g")
          content=$(echo "$content" | sed -e "s/{{.libwebkit2gtk.PackageName}}/libwebkit2gtk-4.1-0/g")
          echo $content
          echo "$content" > build/linux/tiny-rdm_0.0.0_amd64/DEBIAN/control

      - name: Setup app template
        shell: bash
        run: |
          content=$(cat build/linux/tiny-rdm_0.0.0_amd64/usr/share/applications/tiny-rdm.desktop)
          content=$(echo "$content" | sed -e "s/{{.Info.ProductName}}/$(jq -r '.info.productName' wails.json)/g")
          content=$(echo "$content" | sed -e "s/{{.Info.Comments}}/$(jq -r '.info.comments' wails.json)/g")
          echo $content
          echo "$content" > build/linux/tiny-rdm_0.0.0_amd64/usr/share/applications/tiny-rdm.desktop

      - name: Package up deb file
        shell: bash
        run: |
          mv build/bin/tiny-rdm build/linux/tiny-rdm_0.0.0_amd64/usr/local/bin/
          cd build/linux
          mv tiny-rdm_0.0.0_amd64 "tiny-rdm_${{ steps.normalise_version.outputs.version }}_amd64"
          sed -i 's/0.0.0/${{ steps.normalise_version.outputs.version }}/g' "tiny-rdm_${{ steps.normalise_version.outputs.version }}_amd64/DEBIAN/control"
          dpkg-deb --build -Zxz "tiny-rdm_${{ steps.normalise_version.outputs.version }}_amd64"

      - name: Rename deb
        working-directory: ./build/linux
        run: mv "tiny-rdm_${{ steps.normalise_version.outputs.version }}_amd64.deb" "tiny-rdm_${{ steps.normalise_version.outputs.version }}_${{ steps.normalise_platform.outputs.tag }}_webkit2_41.deb"

      - name: Upload release asset
        uses: softprops/action-gh-release@v1
        with:
          tag_name: v${{ steps.normalise_version.outputs.version }}
          files: |
            ./build/linux/tiny-rdm_${{ steps.normalise_version.outputs.version }}_${{ steps.normalise_platform.outputs.tag }}_webkit2_41.deb
          token: ${{ secrets.GITHUB_TOKEN }}
